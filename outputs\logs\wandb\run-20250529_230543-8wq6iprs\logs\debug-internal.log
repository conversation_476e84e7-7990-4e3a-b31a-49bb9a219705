{"time":"2025-05-29T23:05:43.8485284+07:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"outputs\\logs\\wandb\\run-20250529_230543-8wq6iprs\\logs\\debug-core.log"}
{"time":"2025-05-29T23:05:43.9582815+07:00","level":"INFO","msg":"created new stream","id":"8wq6iprs"}
{"time":"2025-05-29T23:05:43.9582815+07:00","level":"INFO","msg":"stream: started","id":"8wq6iprs"}
{"time":"2025-05-29T23:05:43.9582815+07:00","level":"INFO","msg":"handler: started","stream_id":"8wq6iprs"}
{"time":"2025-05-29T23:05:43.9582815+07:00","level":"INFO","msg":"sender: started","stream_id":"8wq6iprs"}
{"time":"2025-05-29T23:05:43.9582815+07:00","level":"INFO","msg":"writer: Do: started","stream_id":"8wq6iprs"}
{"time":"2025-05-29T23:05:44.3989913+07:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-05-29T23:05:59.4172742+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:06:14.4126193+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:06:24.0944342+07:00","level":"INFO","msg":"stream: closing","id":"8wq6iprs"}
{"time":"2025-05-29T23:06:24.0944342+07:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-05-29T23:06:24.0954542+07:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-05-29T23:06:25.6412652+07:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-05-29T23:06:25.9673062+07:00","level":"INFO","msg":"handler: closed","stream_id":"8wq6iprs"}
{"time":"2025-05-29T23:06:25.9673062+07:00","level":"INFO","msg":"sender: closed","stream_id":"8wq6iprs"}
{"time":"2025-05-29T23:06:25.9673062+07:00","level":"INFO","msg":"writer: Close: closed","stream_id":"8wq6iprs"}
{"time":"2025-05-29T23:06:25.9678092+07:00","level":"INFO","msg":"stream: closed","id":"8wq6iprs"}
