#!/usr/bin/env python3
"""
Minimal debug script to isolate the training issue.
"""

import sys
import os
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent))

import torch
import json
from transformers import AutoTokenizer
from thinker_llm.models.integrated_model import IntegratedThinkerLLM
from thinker_llm.utils.config import ModelConfig
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_minimal_forward():
    """Test minimal forward pass with simple data."""
    logger.info("Testing minimal forward pass...")
    
    # Create minimal config
    config = ModelConfig(
        vocab_size=50261,  # Include space for special tokens
        hidden_size=128,   # Small for testing
        num_layers=2,
        num_attention_heads=2,
        thinker_hidden_size=128,
        thinker_num_layers=2,
        use_flash_attention=False,
    )
    
    # Create model
    model = IntegratedThinkerLLM(config)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = model.to(device)
    
    logger.info(f"Model created with vocab_size: {config.vocab_size}")
    logger.info(f"Using device: {device}")
    
    # Create simple test data
    batch_size = 1
    seq_len = 10
    
    # Create input with token IDs in valid range
    input_ids = torch.randint(0, 50257, (batch_size, seq_len), device=device)  # Only use original GPT-2 tokens
    attention_mask = torch.ones_like(input_ids, device=device)
    labels = input_ids.clone()
    
    logger.info(f"Input shape: {input_ids.shape}")
    logger.info(f"Input range: {input_ids.min().item()} to {input_ids.max().item()}")
    
    try:
        # Test forward pass
        model.eval()
        with torch.no_grad():
            outputs = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                labels=labels,
                return_thoughts=True,
            )
        
        logger.info("✓ Forward pass successful!")
        logger.info(f"Output keys: {list(outputs.keys())}")
        
        for key, value in outputs.items():
            if isinstance(value, torch.Tensor):
                logger.info(f"  {key}: shape={value.shape}, dtype={value.dtype}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Forward pass failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_special_tokens():
    """Test with special tokens included."""
    logger.info("Testing with special tokens...")
    
    # Setup tokenizer with special tokens
    tokenizer = AutoTokenizer.from_pretrained("gpt2")
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Add special tokens
    special_tokens = ["<|thinking|>", "<|/thinking|>", "<|response|>", "<|/response|>"]
    new_tokens = []
    for token in special_tokens:
        if token not in tokenizer.get_vocab():
            new_tokens.append(token)
    
    if new_tokens:
        num_added = tokenizer.add_tokens(new_tokens)
        logger.info(f"Added {num_added} special tokens")
    
    vocab_size = len(tokenizer)
    logger.info(f"Final tokenizer vocab size: {vocab_size}")
    
    # Create config with correct vocab size
    config = ModelConfig(
        vocab_size=vocab_size,
        hidden_size=128,
        num_layers=2,
        num_attention_heads=2,
        thinker_hidden_size=128,
        thinker_num_layers=2,
        use_flash_attention=False,
    )
    
    # Create model
    model = IntegratedThinkerLLM(config)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = model.to(device)
    
    # Test with special tokens
    test_text = "Hello <|thinking|> This is a test <|/thinking|> <|response|> Response <|/response|>"
    encoded = tokenizer(test_text, return_tensors="pt", padding=True, truncation=True, max_length=20)
    
    input_ids = encoded["input_ids"].to(device)
    attention_mask = encoded["attention_mask"].to(device)
    labels = input_ids.clone()
    
    logger.info(f"Input shape: {input_ids.shape}")
    logger.info(f"Input range: {input_ids.min().item()} to {input_ids.max().item()}")
    logger.info(f"Vocab size: {vocab_size}")
    
    # Check for out-of-bounds tokens
    max_token = input_ids.max().item()
    if max_token >= vocab_size:
        logger.error(f"Found token ID {max_token} >= vocab size {vocab_size}")
        return False
    
    try:
        model.eval()
        with torch.no_grad():
            outputs = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                labels=labels,
                return_thoughts=True,
            )
        
        logger.info("✓ Forward pass with special tokens successful!")
        return True
        
    except Exception as e:
        logger.error(f"✗ Forward pass with special tokens failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cpu_vs_gpu():
    """Test on CPU vs GPU to isolate CUDA issues."""
    logger.info("Testing CPU vs GPU...")
    
    # Test on CPU first
    logger.info("Testing on CPU...")
    
    config = ModelConfig(
        vocab_size=50261,
        hidden_size=64,
        num_layers=1,
        num_attention_heads=2,
        thinker_hidden_size=64,
        thinker_num_layers=1,
        use_flash_attention=False,
    )
    
    model = IntegratedThinkerLLM(config)
    
    # Test data
    input_ids = torch.randint(0, 50257, (1, 5))
    attention_mask = torch.ones_like(input_ids)
    labels = input_ids.clone()
    
    try:
        model.eval()
        with torch.no_grad():
            outputs = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                labels=labels,
                return_thoughts=True,
            )
        logger.info("✓ CPU test successful!")
        cpu_success = True
    except Exception as e:
        logger.error(f"✗ CPU test failed: {e}")
        cpu_success = False
    
    # Test on GPU if available
    if torch.cuda.is_available():
        logger.info("Testing on GPU...")
        try:
            model = model.cuda()
            input_ids = input_ids.cuda()
            attention_mask = attention_mask.cuda()
            labels = labels.cuda()
            
            model.eval()
            with torch.no_grad():
                outputs = model(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    labels=labels,
                    return_thoughts=True,
                )
            logger.info("✓ GPU test successful!")
            gpu_success = True
        except Exception as e:
            logger.error(f"✗ GPU test failed: {e}")
            gpu_success = False
    else:
        logger.info("CUDA not available, skipping GPU test")
        gpu_success = None
    
    return cpu_success, gpu_success

def main():
    """Main debug function."""
    logger.info("Starting minimal training debug...")
    
    # Test 1: Basic forward pass
    logger.info("\n" + "="*50)
    logger.info("Test 1: Basic forward pass")
    logger.info("="*50)
    test1_success = test_minimal_forward()
    
    # Test 2: With special tokens
    logger.info("\n" + "="*50)
    logger.info("Test 2: With special tokens")
    logger.info("="*50)
    test2_success = test_with_special_tokens()
    
    # Test 3: CPU vs GPU
    logger.info("\n" + "="*50)
    logger.info("Test 3: CPU vs GPU")
    logger.info("="*50)
    cpu_success, gpu_success = test_cpu_vs_gpu()
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("SUMMARY")
    logger.info("="*50)
    logger.info(f"Test 1 (Basic forward): {'✓' if test1_success else '✗'}")
    logger.info(f"Test 2 (Special tokens): {'✓' if test2_success else '✗'}")
    logger.info(f"Test 3 (CPU): {'✓' if cpu_success else '✗'}")
    logger.info(f"Test 3 (GPU): {'✓' if gpu_success else '✗' if gpu_success is not None else 'N/A'}")
    
    if not test1_success:
        logger.error("Basic forward pass is failing - there's a fundamental issue with the model")
    elif not test2_success:
        logger.error("Special tokens are causing issues - vocabulary size problem")
    elif cpu_success and not gpu_success:
        logger.error("GPU-specific issue - likely CUDA memory or kernel problem")
    elif test1_success and test2_success:
        logger.info("Model seems to work fine - issue might be in the training loop or data")

if __name__ == "__main__":
    main()
