"""
<PERSON><PERSON><PERSON> to help prepare your data for ThinkerLLM training.

This script shows how to convert various data formats to the required JSONL format.
"""

import json
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any
import argparse


def convert_csv_to_jsonl(csv_path: str, output_path: str, config: Dict[str, str]):
    """
    Convert CSV data to ThinkerLLM JSONL format.
    
    Args:
        csv_path: Path to CSV file
        output_path: Path for output JSONL file
        config: Column mapping configuration
    """
    df = pd.read_csv(csv_path)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        for _, row in df.iterrows():
            sample = {
                "instruction": row[config.get("instruction_col", "instruction")],
                "thinking": row.get(config.get("thinking_col", "thinking"), ""),
                "output": row[config.get("output_col", "output")],
                "requires_thinking": bool(row.get(config.get("thinking_col", "thinking"), "").strip())
            }
            
            # Add input field if specified
            if config.get("input_col") and config["input_col"] in row:
                sample["input"] = row[config["input_col"]]
            
            f.write(json.dumps(sample) + '\n')
    
    print(f"Converted {len(df)} samples from {csv_path} to {output_path}")


def convert_json_to_jsonl(json_path: str, output_path: str, config: Dict[str, str]):
    """
    Convert JSON data to ThinkerLLM JSONL format.
    
    Args:
        json_path: Path to JSON file
        output_path: Path for output JSONL file
        config: Field mapping configuration
    """
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Handle different JSON structures
    if isinstance(data, dict) and "data" in data:
        data = data["data"]
    elif isinstance(data, dict) and "examples" in data:
        data = data["examples"]
    
    with open(output_path, 'w', encoding='utf-8') as f:
        for item in data:
            sample = {
                "instruction": item.get(config.get("instruction_field", "instruction"), ""),
                "thinking": item.get(config.get("thinking_field", "thinking"), ""),
                "output": item.get(config.get("output_field", "output"), ""),
                "requires_thinking": bool(item.get(config.get("thinking_field", "thinking"), "").strip())
            }
            
            # Add input field if specified
            if config.get("input_field") and config["input_field"] in item:
                sample["input"] = item[config["input_field"]]
            
            f.write(json.dumps(sample) + '\n')
    
    print(f"Converted {len(data)} samples from {json_path} to {output_path}")


def add_thinking_to_data(input_path: str, output_path: str, thinking_ratio: float = 0.3):
    """
    Add thinking processes to existing data that doesn't have them.
    
    Args:
        input_path: Path to input JSONL file
        output_path: Path for output JSONL file with thinking
        thinking_ratio: Proportion of samples to add thinking to
    """
    import random
    
    # Simple thinking templates based on question types
    thinking_templates = {
        "math": "Let me work through this step by step.\n\nFirst, I need to identify what's being asked.\nThen, I'll solve it systematically.\nFinally, I'll verify my answer.",
        "analysis": "I need to analyze this carefully.\n\nLet me consider the key factors:\n1. The main components\n2. Their relationships\n3. The implications\n\nBased on this analysis, I can provide a comprehensive answer.",
        "explanation": "This requires a clear explanation.\n\nI should break this down into:\n1. The basic concept\n2. How it works\n3. Why it's important\n\nThis will help provide a thorough understanding.",
        "comparison": "I need to compare these options systematically.\n\nLet me evaluate:\n1. The similarities\n2. The differences\n3. The pros and cons of each\n\nThis will help make an informed comparison.",
        "default": "Let me think about this carefully.\n\nI should consider the key aspects of this question and provide a thoughtful response."
    }
    
    def get_thinking_template(instruction: str) -> str:
        instruction_lower = instruction.lower()
        if any(word in instruction_lower for word in ["calculate", "solve", "math", "equation"]):
            return thinking_templates["math"]
        elif any(word in instruction_lower for word in ["analyze", "examine", "evaluate"]):
            return thinking_templates["analysis"]
        elif any(word in instruction_lower for word in ["explain", "describe", "what is", "how does"]):
            return thinking_templates["explanation"]
        elif any(word in instruction_lower for word in ["compare", "contrast", "difference", "versus"]):
            return thinking_templates["comparison"]
        else:
            return thinking_templates["default"]
    
    with open(input_path, 'r', encoding='utf-8') as f:
        samples = [json.loads(line) for line in f]
    
    with open(output_path, 'w', encoding='utf-8') as f:
        for sample in samples:
            # Decide if this sample should have thinking
            if random.random() < thinking_ratio and not sample.get("thinking", "").strip():
                sample["thinking"] = get_thinking_template(sample["instruction"])
                sample["requires_thinking"] = True
            else:
                sample["requires_thinking"] = bool(sample.get("thinking", "").strip())
            
            f.write(json.dumps(sample) + '\n')
    
    print(f"Added thinking to {output_path} with {thinking_ratio:.1%} thinking ratio")


def validate_data(jsonl_path: str) -> Dict[str, Any]:
    """
    Validate JSONL data for ThinkerLLM training.
    
    Args:
        jsonl_path: Path to JSONL file
        
    Returns:
        Validation statistics
    """
    with open(jsonl_path, 'r', encoding='utf-8') as f:
        samples = [json.loads(line) for line in f]
    
    stats = {
        "total_samples": len(samples),
        "thinking_samples": 0,
        "non_thinking_samples": 0,
        "avg_instruction_length": 0,
        "avg_thinking_length": 0,
        "avg_output_length": 0,
        "missing_fields": [],
    }
    
    instruction_lengths = []
    thinking_lengths = []
    output_lengths = []
    
    for i, sample in enumerate(samples):
        # Check required fields
        required_fields = ["instruction", "output"]
        for field in required_fields:
            if field not in sample or not sample[field]:
                stats["missing_fields"].append(f"Sample {i}: missing {field}")
        
        # Count thinking vs non-thinking
        if sample.get("thinking", "").strip():
            stats["thinking_samples"] += 1
            thinking_lengths.append(len(sample["thinking"]))
        else:
            stats["non_thinking_samples"] += 1
        
        # Collect lengths
        instruction_lengths.append(len(sample.get("instruction", "")))
        output_lengths.append(len(sample.get("output", "")))
    
    # Calculate averages
    stats["avg_instruction_length"] = sum(instruction_lengths) / len(instruction_lengths) if instruction_lengths else 0
    stats["avg_thinking_length"] = sum(thinking_lengths) / len(thinking_lengths) if thinking_lengths else 0
    stats["avg_output_length"] = sum(output_lengths) / len(output_lengths) if output_lengths else 0
    stats["thinking_ratio"] = stats["thinking_samples"] / stats["total_samples"] if stats["total_samples"] > 0 else 0
    
    return stats


def main():
    parser = argparse.ArgumentParser(description="Prepare data for ThinkerLLM training")
    parser.add_argument("--input", required=True, help="Input file path")
    parser.add_argument("--output", required=True, help="Output JSONL file path")
    parser.add_argument("--format", choices=["csv", "json", "jsonl"], required=True, help="Input format")
    parser.add_argument("--add-thinking", action="store_true", help="Add thinking to samples that don't have it")
    parser.add_argument("--thinking-ratio", type=float, default=0.3, help="Ratio of samples to add thinking to")
    parser.add_argument("--validate", action="store_true", help="Validate the output data")
    
    # Column/field mapping arguments
    parser.add_argument("--instruction-col", default="instruction", help="Instruction column/field name")
    parser.add_argument("--thinking-col", default="thinking", help="Thinking column/field name")
    parser.add_argument("--output-col", default="output", help="Output column/field name")
    parser.add_argument("--input-col", help="Input column/field name (optional)")
    
    args = parser.parse_args()
    
    # Create output directory if it doesn't exist
    Path(args.output).parent.mkdir(parents=True, exist_ok=True)
    
    # Convert data based on format
    config = {
        "instruction_col": args.instruction_col,
        "thinking_col": args.thinking_col,
        "output_col": args.output_col,
        "input_col": args.input_col,
        "instruction_field": args.instruction_col,
        "thinking_field": args.thinking_col,
        "output_field": args.output_col,
        "input_field": args.input_col,
    }
    
    if args.format == "csv":
        convert_csv_to_jsonl(args.input, args.output, config)
    elif args.format == "json":
        convert_json_to_jsonl(args.input, args.output, config)
    elif args.format == "jsonl":
        # Just copy if already JSONL
        import shutil
        shutil.copy(args.input, args.output)
        print(f"Copied {args.input} to {args.output}")
    
    # Add thinking if requested
    if args.add_thinking:
        temp_output = args.output + ".tmp"
        add_thinking_to_data(args.output, temp_output, args.thinking_ratio)
        Path(args.output).unlink()
        Path(temp_output).rename(args.output)
    
    # Validate if requested
    if args.validate:
        stats = validate_data(args.output)
        print("\n" + "="*50)
        print("DATA VALIDATION RESULTS")
        print("="*50)
        print(f"Total samples: {stats['total_samples']}")
        print(f"Thinking samples: {stats['thinking_samples']} ({stats['thinking_ratio']:.1%})")
        print(f"Non-thinking samples: {stats['non_thinking_samples']}")
        print(f"Average instruction length: {stats['avg_instruction_length']:.1f} chars")
        print(f"Average thinking length: {stats['avg_thinking_length']:.1f} chars")
        print(f"Average output length: {stats['avg_output_length']:.1f} chars")
        
        if stats['missing_fields']:
            print(f"\nWarnings: {len(stats['missing_fields'])} missing fields found")
            for warning in stats['missing_fields'][:5]:  # Show first 5
                print(f"  - {warning}")
            if len(stats['missing_fields']) > 5:
                print(f"  ... and {len(stats['missing_fields']) - 5} more")
        else:
            print("\n✅ All samples have required fields")


if __name__ == "__main__":
    main()
