"""
Inference configuration for ThinkerLLM.
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any
import json
import yaml
from pathlib import Path


@dataclass
class InferenceConfig:
    """Configuration for ThinkerLLM inference."""
    
    # Generation Parameters
    max_new_tokens: int = 512
    temperature: float = 0.7
    top_p: float = 0.9
    top_k: int = 50
    do_sample: bool = True
    num_beams: int = 1
    
    # Thinking Parameters
    show_thinking: bool = True
    thinking_temperature: float = 0.8
    max_thinking_tokens: int = 256
    
    # Decision Parameters
    decision_threshold: float = 0.5
    force_thinking: bool = False
    disable_thinking: bool = False
    
    # Performance
    batch_size: int = 1
    use_cache: bool = True
    
    # Output Formatting
    include_metadata: bool = True
    format_thoughts: bool = True
    clean_output: bool = True
    
    def save(self, path: str):
        """Save configuration to file."""
        path = Path(path)
        if path.suffix == '.json':
            with open(path, 'w') as f:
                json.dump(self.__dict__, f, indent=2)
        elif path.suffix in ['.yaml', '.yml']:
            with open(path, 'w') as f:
                yaml.dump(self.__dict__, f, default_flow_style=False)
        else:
            raise ValueError(f"Unsupported file format: {path.suffix}")
    
    @classmethod
    def load(cls, path: str) -> 'InferenceConfig':
        """Load configuration from file."""
        path = Path(path)
        if path.suffix == '.json':
            with open(path, 'r') as f:
                config_dict = json.load(f)
        elif path.suffix in ['.yaml', '.yml']:
            with open(path, 'r') as f:
                config_dict = yaml.safe_load(f)
        else:
            raise ValueError(f"Unsupported file format: {path.suffix}")
        
        return cls(**config_dict)
    
    def update(self, **kwargs):
        """Update configuration parameters."""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                raise ValueError(f"Unknown parameter: {key}")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return self.__dict__.copy()
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'InferenceConfig':
        """Create from dictionary."""
        return cls(**config_dict)
