#!/usr/bin/env python3
"""
Debug script to test the actual training loop components.
"""

import sys
import os
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent))

import torch
import json
from transformers import AutoTokenizer
from thinker_llm.models.integrated_model import Integrated<PERSON>hinkerLL<PERSON>
from thinker_llm.utils.config import ModelConfig, TrainingConfig
from thinker_llm.training.data_loader import ThinkerDataset, ThinkerDataLoader
from thinker_llm.training.losses import CombinedLoss
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_data_loading():
    """Test data loading with actual data files."""
    logger.info("Testing data loading...")
    
    # Setup tokenizer
    tokenizer = AutoTokenizer.from_pretrained("gpt2")
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Add special tokens
    special_tokens = ["<|thinking|>", "<|/thinking|>", "<|response|>", "<|/response|>"]
    new_tokens = []
    for token in special_tokens:
        if token not in tokenizer.get_vocab():
            new_tokens.append(token)
    
    if new_tokens:
        num_added = tokenizer.add_tokens(new_tokens)
        logger.info(f"Added {num_added} special tokens")
    
    vocab_size = len(tokenizer)
    logger.info(f"Tokenizer vocab size: {vocab_size}")
    
    # Test both data files
    data_files = ["safe_data/train.jsonl", "data/train.jsonl"]
    
    for data_path in data_files:
        if not os.path.exists(data_path):
            logger.warning(f"Data file not found: {data_path}")
            continue
            
        logger.info(f"\nTesting data file: {data_path}")
        
        try:
            # Create dataset
            dataset = ThinkerDataset(
                data_path=data_path,
                tokenizer=tokenizer,
                max_length=128,  # Smaller for testing
                thinking_ratio=0.3,
                split="train",
            )
            
            logger.info(f"Dataset created with {len(dataset)} samples")
            
            # Test first few samples
            for i in range(min(3, len(dataset))):
                sample = dataset[i]
                logger.info(f"Sample {i}:")
                
                for key, value in sample.items():
                    if isinstance(value, torch.Tensor):
                        min_val = value.min().item()
                        max_val = value.max().item()
                        logger.info(f"  {key}: shape={value.shape}, range=[{min_val}, {max_val}]")
                        
                        # Check for out-of-bounds values
                        if key in ['input_ids', 'thinking_ids'] and max_val >= vocab_size:
                            logger.error(f"Found token ID {max_val} >= vocab size {vocab_size} in {key}")
                            return False
                    else:
                        logger.info(f"  {key}: {value}")
            
            # Test dataloader
            dataloader = ThinkerDataLoader(
                dataset=dataset,
                batch_size=1,
                shuffle=False,
            )
            
            data_iter = iter(dataloader.get_dataloader())
            batch = next(data_iter)
            
            logger.info(f"Batch loaded successfully")
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    min_val = value.min().item()
                    max_val = value.max().item()
                    logger.info(f"  {key}: shape={value.shape}, range=[{min_val}, {max_val}]")
                    
                    if key in ['input_ids', 'thinking_labels'] and max_val >= vocab_size:
                        logger.error(f"Found token ID {max_val} >= vocab size {vocab_size} in batch {key}")
                        return False
            
            logger.info(f"✓ Data loading successful for {data_path}")
            
        except Exception as e:
            logger.error(f"✗ Data loading failed for {data_path}: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    return True

def test_model_with_real_data():
    """Test model forward pass with real data."""
    logger.info("Testing model with real data...")
    
    # Setup tokenizer and model
    tokenizer = AutoTokenizer.from_pretrained("gpt2")
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    special_tokens = ["<|thinking|>", "<|/thinking|>", "<|response|>", "<|/response|>"]
    new_tokens = []
    for token in special_tokens:
        if token not in tokenizer.get_vocab():
            new_tokens.append(token)
    
    if new_tokens:
        tokenizer.add_tokens(new_tokens)
    
    vocab_size = len(tokenizer)
    
    config = ModelConfig(
        vocab_size=vocab_size,
        hidden_size=128,
        num_layers=2,
        num_attention_heads=2,
        thinker_hidden_size=128,
        thinker_num_layers=2,
        use_flash_attention=False,
    )
    
    model = IntegratedThinkerLLM(config)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = model.to(device)
    
    # Test with safe data
    data_path = "safe_data/train.jsonl"
    if os.path.exists(data_path):
        logger.info(f"Testing with {data_path}")
        
        dataset = ThinkerDataset(
            data_path=data_path,
            tokenizer=tokenizer,
            max_length=128,
            thinking_ratio=0.3,
            split="train",
        )
        
        dataloader = ThinkerDataLoader(
            dataset=dataset,
            batch_size=1,
            shuffle=False,
        )
        
        data_iter = iter(dataloader.get_dataloader())
        batch = next(data_iter)
        
        # Move to device
        batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v
                for k, v in batch.items()}
        
        try:
            model.eval()
            with torch.no_grad():
                outputs = model(
                    input_ids=batch["input_ids"],
                    attention_mask=batch["attention_mask"],
                    labels=batch["labels"],
                    return_thoughts=True,
                )
            
            logger.info("✓ Model forward pass with real data successful!")
            return True
            
        except Exception as e:
            logger.error(f"✗ Model forward pass failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    return False

def test_loss_computation():
    """Test loss computation with real data."""
    logger.info("Testing loss computation...")
    
    # Setup everything
    tokenizer = AutoTokenizer.from_pretrained("gpt2")
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    special_tokens = ["<|thinking|>", "<|/thinking|>", "<|response|>", "<|/response|>"]
    new_tokens = []
    for token in special_tokens:
        if token not in tokenizer.get_vocab():
            new_tokens.append(token)
    
    if new_tokens:
        tokenizer.add_tokens(new_tokens)
    
    vocab_size = len(tokenizer)
    
    config = ModelConfig(
        vocab_size=vocab_size,
        hidden_size=128,
        num_layers=2,
        num_attention_heads=2,
        thinker_hidden_size=128,
        thinker_num_layers=2,
        use_flash_attention=False,
    )
    
    model = IntegratedThinkerLLM(config)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = model.to(device)
    
    # Create loss function
    loss_fn = CombinedLoss(
        vocab_size=vocab_size,
        llm_weight=1.0,
        thinker_weight=0.5,
        decision_weight=0.3,
    )
    
    # Test with safe data
    data_path = "safe_data/train.jsonl"
    if os.path.exists(data_path):
        logger.info(f"Testing loss computation with {data_path}")
        
        dataset = ThinkerDataset(
            data_path=data_path,
            tokenizer=tokenizer,
            max_length=128,
            thinking_ratio=0.3,
            split="train",
        )
        
        dataloader = ThinkerDataLoader(
            dataset=dataset,
            batch_size=1,
            shuffle=False,
        )
        
        data_iter = iter(dataloader.get_dataloader())
        batch = next(data_iter)
        
        # Move to device
        batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v
                for k, v in batch.items()}
        
        try:
            model.eval()
            with torch.no_grad():
                # Forward pass
                outputs = model(
                    input_ids=batch["input_ids"],
                    attention_mask=batch["attention_mask"],
                    labels=batch["labels"],
                    return_thoughts=True,
                )
                
                # Loss computation
                decision_labels = batch.get("requires_thinking")
                thinking_labels = batch.get("thinking_labels")
                
                if decision_labels is not None:
                    decision_labels = decision_labels.to(device)
                if thinking_labels is not None:
                    thinking_labels = thinking_labels.to(device)
                
                loss_dict = loss_fn(
                    model_outputs=outputs,
                    labels=batch["labels"],
                    thinking_labels=thinking_labels,
                    decision_labels=decision_labels,
                )
                
                logger.info("✓ Loss computation successful!")
                logger.info(f"Loss components: {list(loss_dict.keys())}")
                for key, value in loss_dict.items():
                    if isinstance(value, torch.Tensor):
                        logger.info(f"  {key}: {value.item():.4f}")
                
                return True
                
        except Exception as e:
            logger.error(f"✗ Loss computation failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    return False

def main():
    """Main debug function."""
    logger.info("Starting training loop debug...")
    
    # Test 1: Data loading
    logger.info("\n" + "="*50)
    logger.info("Test 1: Data loading")
    logger.info("="*50)
    test1_success = test_data_loading()
    
    # Test 2: Model with real data
    logger.info("\n" + "="*50)
    logger.info("Test 2: Model with real data")
    logger.info("="*50)
    test2_success = test_model_with_real_data()
    
    # Test 3: Loss computation
    logger.info("\n" + "="*50)
    logger.info("Test 3: Loss computation")
    logger.info("="*50)
    test3_success = test_loss_computation()
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("SUMMARY")
    logger.info("="*50)
    logger.info(f"Test 1 (Data loading): {'✓' if test1_success else '✗'}")
    logger.info(f"Test 2 (Model forward): {'✓' if test2_success else '✗'}")
    logger.info(f"Test 3 (Loss computation): {'✓' if test3_success else '✗'}")
    
    if all([test1_success, test2_success, test3_success]):
        logger.info("All tests passed! The issue might be in the training loop itself or gradient computation.")
    else:
        logger.error("Some tests failed - this helps narrow down the issue.")

if __name__ == "__main__":
    main()
