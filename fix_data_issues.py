#!/usr/bin/env python3
"""
Script to identify and fix data issues that cause CUDA assertion errors.
"""

import sys
import os
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent))

import torch
import json
from transformers import AutoTokenizer
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_data_file(data_path: str, tokenizer):
    """Analyze data file for potential issues."""
    logger.info(f"Analyzing data file: {data_path}")
    
    issues = []
    total_samples = 0
    max_token_id = 0
    min_token_id = float('inf')
    vocab_size = len(tokenizer)
    
    with open(data_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            try:
                sample = json.loads(line.strip())
                total_samples += 1
                
                # Check required fields
                required_fields = ['instruction', 'thinking', 'output']
                missing_fields = [field for field in required_fields if field not in sample]
                if missing_fields:
                    issues.append(f"Line {line_num}: Missing fields: {missing_fields}")
                
                # Tokenize and check token IDs
                full_text = f"{sample.get('instruction', '')} {sample.get('thinking', '')} {sample.get('output', '')}"
                
                try:
                    tokens = tokenizer.encode(full_text, add_special_tokens=True)
                    
                    for token_id in tokens:
                        max_token_id = max(max_token_id, token_id)
                        min_token_id = min(min_token_id, token_id)
                        
                        if token_id >= vocab_size:
                            issues.append(f"Line {line_num}: Token ID {token_id} >= vocab_size {vocab_size}")
                        if token_id < 0:
                            issues.append(f"Line {line_num}: Negative token ID {token_id}")
                            
                except Exception as e:
                    issues.append(f"Line {line_num}: Tokenization error: {e}")
                
                # Check for extremely long sequences
                if len(full_text) > 10000:
                    issues.append(f"Line {line_num}: Very long text ({len(full_text)} chars)")
                
                # Check for empty fields
                if not sample.get('instruction', '').strip():
                    issues.append(f"Line {line_num}: Empty instruction")
                if not sample.get('output', '').strip():
                    issues.append(f"Line {line_num}: Empty output")
                    
            except json.JSONDecodeError as e:
                issues.append(f"Line {line_num}: JSON decode error: {e}")
            except Exception as e:
                issues.append(f"Line {line_num}: Unexpected error: {e}")
    
    logger.info(f"Analysis complete:")
    logger.info(f"  Total samples: {total_samples}")
    logger.info(f"  Token ID range: {min_token_id} to {max_token_id}")
    logger.info(f"  Vocabulary size: {vocab_size}")
    logger.info(f"  Issues found: {len(issues)}")
    
    if issues:
        logger.warning("Issues found:")
        for issue in issues[:20]:  # Show first 20 issues
            logger.warning(f"  {issue}")
        if len(issues) > 20:
            logger.warning(f"  ... and {len(issues) - 20} more issues")
    
    return issues, max_token_id, min_token_id

def clean_data_file(input_path: str, output_path: str, tokenizer):
    """Clean data file by removing problematic samples."""
    logger.info(f"Cleaning data file: {input_path} -> {output_path}")
    
    vocab_size = len(tokenizer)
    cleaned_samples = 0
    removed_samples = 0
    
    with open(input_path, 'r', encoding='utf-8') as infile, \
         open(output_path, 'w', encoding='utf-8') as outfile:
        
        for line_num, line in enumerate(infile, 1):
            try:
                sample = json.loads(line.strip())
                
                # Check required fields
                required_fields = ['instruction', 'thinking', 'output']
                if not all(field in sample for field in required_fields):
                    removed_samples += 1
                    continue
                
                # Check for empty fields
                if not sample.get('instruction', '').strip() or not sample.get('output', '').strip():
                    removed_samples += 1
                    continue
                
                # Check token IDs
                full_text = f"{sample['instruction']} {sample['thinking']} {sample['output']}"
                
                try:
                    tokens = tokenizer.encode(full_text, add_special_tokens=True, max_length=2048, truncation=True)
                    
                    # Check for out-of-bounds token IDs
                    if any(token_id >= vocab_size or token_id < 0 for token_id in tokens):
                        removed_samples += 1
                        continue
                    
                    # Truncate very long sequences
                    if len(full_text) > 8000:
                        # Truncate thinking and output proportionally
                        max_instruction = 1000
                        max_thinking = 3000
                        max_output = 4000
                        
                        if len(sample['instruction']) > max_instruction:
                            sample['instruction'] = sample['instruction'][:max_instruction] + "..."
                        if len(sample['thinking']) > max_thinking:
                            sample['thinking'] = sample['thinking'][:max_thinking] + "..."
                        if len(sample['output']) > max_output:
                            sample['output'] = sample['output'][:max_output] + "..."
                    
                    # Write cleaned sample
                    outfile.write(json.dumps(sample, ensure_ascii=False) + '\n')
                    cleaned_samples += 1
                    
                except Exception as e:
                    logger.warning(f"Line {line_num}: Tokenization error, removing: {e}")
                    removed_samples += 1
                    continue
                    
            except json.JSONDecodeError as e:
                logger.warning(f"Line {line_num}: JSON decode error, removing: {e}")
                removed_samples += 1
                continue
            except Exception as e:
                logger.warning(f"Line {line_num}: Unexpected error, removing: {e}")
                removed_samples += 1
                continue
    
    logger.info(f"Cleaning complete:")
    logger.info(f"  Cleaned samples: {cleaned_samples}")
    logger.info(f"  Removed samples: {removed_samples}")
    logger.info(f"  Cleaned file saved to: {output_path}")
    
    return cleaned_samples, removed_samples

def validate_cleaned_data(data_path: str, tokenizer):
    """Validate that cleaned data has no issues."""
    logger.info(f"Validating cleaned data: {data_path}")
    
    issues, max_token_id, min_token_id = analyze_data_file(data_path, tokenizer)
    
    if not issues:
        logger.info("✅ Cleaned data validation passed!")
        return True
    else:
        logger.error("❌ Cleaned data still has issues!")
        return False

def main():
    """Main function."""
    logger.info("Starting data issue analysis and fixing...")
    
    # Setup tokenizer with special tokens
    tokenizer = AutoTokenizer.from_pretrained("gpt2")
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Add special tokens
    special_tokens = ["<|thinking|>", "<|/thinking|>", "<|response|>", "<|/response|>"]
    new_tokens = []
    for token in special_tokens:
        if token not in tokenizer.get_vocab():
            new_tokens.append(token)
    
    if new_tokens:
        num_added = tokenizer.add_tokens(new_tokens)
        logger.info(f"Added {num_added} special tokens to tokenizer")
    
    vocab_size = len(tokenizer)
    logger.info(f"Final tokenizer vocab size: {vocab_size}")
    
    # Analyze original data
    data_path = "data/train.jsonl"
    if not os.path.exists(data_path):
        logger.error(f"Data file not found: {data_path}")
        return
    
    logger.info("="*50)
    logger.info("ANALYZING ORIGINAL DATA")
    logger.info("="*50)
    issues, max_token_id, min_token_id = analyze_data_file(data_path, tokenizer)
    
    if issues:
        # Clean the data
        logger.info("="*50)
        logger.info("CLEANING DATA")
        logger.info("="*50)
        cleaned_path = "data/train_cleaned.jsonl"
        cleaned_samples, removed_samples = clean_data_file(data_path, cleaned_path, tokenizer)
        
        # Validate cleaned data
        logger.info("="*50)
        logger.info("VALIDATING CLEANED DATA")
        logger.info("="*50)
        is_valid = validate_cleaned_data(cleaned_path, tokenizer)
        
        if is_valid:
            logger.info("="*50)
            logger.info("RECOMMENDATION")
            logger.info("="*50)
            logger.info(f"✅ Use the cleaned data file for training:")
            logger.info(f"   python scripts/train_thinker_llm.py --config configs/training_config.yaml --train-data data/train_cleaned.jsonl --batch-size 1 --num-epochs 1 --strategy joint")
        else:
            logger.error("❌ Cleaned data still has issues. Manual inspection required.")
    else:
        logger.info("✅ Original data has no issues!")

if __name__ == "__main__":
    main()
