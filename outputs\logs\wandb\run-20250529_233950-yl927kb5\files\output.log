INFO:__main__:Starting training...
INFO:thinker_llm.training.trainer:Starting training...
INFO:thinker_llm.training.trainer:Total epochs: 1
INFO:thinker_llm.training.trainer:Total steps: 19944
Epoch 1/1:   0%|                                                                                                                             | 0/19944 [00:00<?, ?it/s]d:\vlinhd11\projects\lcms_llm\moe-llm\geneticai\v4\thinker_llm\training\trainer.py:200: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
  with autocast( dtype=self.mixed_precision_dtype):
ERROR:__main__:Training failed with error: Attempting to unscale FP16 gradients.                                                                                       
Traceback (most recent call last):
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\scripts\train_thinker_llm.py", line 280, in <module>
    main()
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\scripts\train_thinker_llm.py", line 244, in main
    trainer.train()
  File "d:\vlinhd11\projects\lcms_llm\moe-llm\geneticai\v4\thinker_llm\training\trainer.py", line 442, in train
    epoch_metrics = self.train_epoch()
                    ^^^^^^^^^^^^^^^^^^
  File "d:\vlinhd11\projects\lcms_llm\moe-llm\geneticai\v4\thinker_llm\training\trainer.py", line 275, in train_epoch
    self.scaler.unscale_(self.optimizer)
  File "D:\.conda\envs\tts\Lib\site-packages\torch\amp\grad_scaler.py", line 342, in unscale_
    optimizer_state["found_inf_per_device"] = self._unscale_grads_(
                                              ^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\amp\grad_scaler.py", line 264, in _unscale_grads_
    raise ValueError("Attempting to unscale FP16 gradients.")
ValueError: Attempting to unscale FP16 gradients.
