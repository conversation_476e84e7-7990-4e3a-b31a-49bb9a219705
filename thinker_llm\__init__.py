"""
ThinkerLLM: Large Language Model with Integrated ThinkerModule

A modular implementation of an LLM architecture that combines traditional
autoregressive generation with non-autoregressive reasoning capabilities.
"""

__version__ = "0.1.0"
__author__ = "Your Name"

# Import core components
from .utils.config import ModelConfig, TrainingConfig

__all__ = [
    "ModelConfig",
    "TrainingConfig",
]
