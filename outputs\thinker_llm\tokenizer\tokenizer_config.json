{"add_prefix_space": false, "added_tokens_decoder": {"50256": {"content": "<|endoftext|>", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": true}, "50257": {"content": "<|thinking|>", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50258": {"content": "<|/thinking|>", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50259": {"content": "<|response|>", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "50260": {"content": "<|/response|>", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}}, "bos_token": "<|endoftext|>", "clean_up_tokenization_spaces": false, "eos_token": "<|endoftext|>", "extra_special_tokens": {}, "model_max_length": 1024, "pad_token": "<|endoftext|>", "tokenizer_class": "GPT2Tokenizer", "unk_token": "<|endoftext|>"}