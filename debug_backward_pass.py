#!/usr/bin/env python3
"""
Debug script to test the backward pass specifically.
"""

import sys
import os
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent))

import torch
import json
from transformers import AutoTokenizer
from thinker_llm.models.integrated_model import IntegratedThinkerLLM
from thinker_llm.utils.config import ModelConfig, TrainingConfig
from thinker_llm.training.data_loader import ThinkerDataset, ThinkerDataLoader
from thinker_llm.training.losses import CombinedLoss
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_backward_pass():
    """Test backward pass with gradient computation."""
    logger.info("Testing backward pass...")
    
    # Setup everything
    tokenizer = AutoTokenizer.from_pretrained("gpt2")
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    special_tokens = ["<|thinking|>", "<|/thinking|>", "<|response|>", "<|/response|>"]
    new_tokens = []
    for token in special_tokens:
        if token not in tokenizer.get_vocab():
            new_tokens.append(token)
    
    if new_tokens:
        tokenizer.add_tokens(new_tokens)
    
    vocab_size = len(tokenizer)
    
    config = ModelConfig(
        vocab_size=vocab_size,
        hidden_size=128,
        num_layers=2,
        num_attention_heads=2,
        thinker_hidden_size=128,
        thinker_num_layers=2,
        use_flash_attention=False,
    )
    
    model = IntegratedThinkerLLM(config)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = model.to(device)
    model.train()  # Important: set to training mode
    
    # Create loss function
    loss_fn = CombinedLoss(
        vocab_size=vocab_size,
        llm_weight=1.0,
        thinker_weight=0.5,
        decision_weight=0.3,
    )
    
    # Create optimizer
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4)
    
    # Test with safe data first
    data_path = "safe_data/train.jsonl"
    if os.path.exists(data_path):
        logger.info(f"Testing backward pass with {data_path}")
        
        dataset = ThinkerDataset(
            data_path=data_path,
            tokenizer=tokenizer,
            max_length=64,  # Even smaller for testing
            thinking_ratio=0.3,
            split="train",
        )
        
        dataloader = ThinkerDataLoader(
            dataset=dataset,
            batch_size=1,
            shuffle=False,
        )
        
        data_iter = iter(dataloader.get_dataloader())
        
        for step in range(min(3, len(dataset))):  # Test first 3 samples
            try:
                batch = next(data_iter)
                
                # Move to device
                batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v
                        for k, v in batch.items()}
                
                logger.info(f"Step {step}: Testing backward pass...")
                
                # Zero gradients
                optimizer.zero_grad()
                
                # Forward pass
                outputs = model(
                    input_ids=batch["input_ids"],
                    attention_mask=batch["attention_mask"],
                    labels=batch["labels"],
                    return_thoughts=True,
                )
                
                # Loss computation
                decision_labels = batch.get("requires_thinking")
                thinking_labels = batch.get("thinking_labels")
                
                if decision_labels is not None:
                    decision_labels = decision_labels.to(device)
                if thinking_labels is not None:
                    thinking_labels = thinking_labels.to(device)
                
                loss_dict = loss_fn(
                    model_outputs=outputs,
                    labels=batch["labels"],
                    thinking_labels=thinking_labels,
                    decision_labels=decision_labels,
                )
                
                loss = loss_dict["total_loss"]
                logger.info(f"  Loss: {loss.item():.4f}")
                
                # Backward pass
                loss.backward()
                
                # Check gradients
                grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                logger.info(f"  Gradient norm: {grad_norm:.4f}")
                
                # Optimizer step
                optimizer.step()
                
                logger.info(f"  ✓ Step {step} successful!")
                
            except Exception as e:
                logger.error(f"  ✗ Step {step} failed: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        logger.info("✓ Backward pass test with safe data successful!")
        
        # Now test with real data
        data_path = "data/train.jsonl"
        if os.path.exists(data_path):
            logger.info(f"\nTesting backward pass with {data_path}")
            
            dataset = ThinkerDataset(
                data_path=data_path,
                tokenizer=tokenizer,
                max_length=64,
                thinking_ratio=0.3,
                split="train",
            )
            
            dataloader = ThinkerDataLoader(
                dataset=dataset,
                batch_size=1,
                shuffle=False,
            )
            
            data_iter = iter(dataloader.get_dataloader())
            
            for step in range(min(3, len(dataset))):  # Test first 3 samples
                try:
                    batch = next(data_iter)
                    
                    # Move to device
                    batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v
                            for k, v in batch.items()}
                    
                    logger.info(f"Step {step}: Testing backward pass with real data...")
                    
                    # Zero gradients
                    optimizer.zero_grad()
                    
                    # Forward pass
                    outputs = model(
                        input_ids=batch["input_ids"],
                        attention_mask=batch["attention_mask"],
                        labels=batch["labels"],
                        return_thoughts=True,
                    )
                    
                    # Loss computation
                    decision_labels = batch.get("requires_thinking")
                    thinking_labels = batch.get("thinking_labels")
                    
                    if decision_labels is not None:
                        decision_labels = decision_labels.to(device)
                    if thinking_labels is not None:
                        thinking_labels = thinking_labels.to(device)
                    
                    loss_dict = loss_fn(
                        model_outputs=outputs,
                        labels=batch["labels"],
                        thinking_labels=thinking_labels,
                        decision_labels=decision_labels,
                    )
                    
                    loss = loss_dict["total_loss"]
                    logger.info(f"  Loss: {loss.item():.4f}")
                    
                    # Backward pass
                    loss.backward()
                    
                    # Check gradients
                    grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    logger.info(f"  Gradient norm: {grad_norm:.4f}")
                    
                    # Optimizer step
                    optimizer.step()
                    
                    logger.info(f"  ✓ Step {step} with real data successful!")
                    
                except Exception as e:
                    logger.error(f"  ✗ Step {step} with real data failed: {e}")
                    import traceback
                    traceback.print_exc()
                    return False
            
            logger.info("✓ Backward pass test with real data successful!")
        
        return True
    
    return False

def test_memory_usage():
    """Test memory usage during training."""
    logger.info("Testing memory usage...")
    
    if not torch.cuda.is_available():
        logger.info("CUDA not available, skipping memory test")
        return True
    
    # Clear cache
    torch.cuda.empty_cache()
    
    initial_memory = torch.cuda.memory_allocated()
    logger.info(f"Initial GPU memory: {initial_memory / 1024**2:.1f} MB")
    
    # Setup model
    tokenizer = AutoTokenizer.from_pretrained("gpt2")
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    special_tokens = ["<|thinking|>", "<|/thinking|>", "<|response|>", "<|/response|>"]
    new_tokens = []
    for token in special_tokens:
        if token not in tokenizer.get_vocab():
            new_tokens.append(token)
    
    if new_tokens:
        tokenizer.add_tokens(new_tokens)
    
    vocab_size = len(tokenizer)
    
    config = ModelConfig(
        vocab_size=vocab_size,
        hidden_size=256,  # Larger to see memory usage
        num_layers=4,
        num_attention_heads=4,
        thinker_hidden_size=256,
        thinker_num_layers=4,
        use_flash_attention=False,
    )
    
    model = IntegratedThinkerLLM(config)
    model = model.cuda()
    
    model_memory = torch.cuda.memory_allocated()
    logger.info(f"After model creation: {model_memory / 1024**2:.1f} MB")
    
    # Test with different batch sizes
    for batch_size in [1, 2, 4]:
        logger.info(f"\nTesting batch size {batch_size}")
        
        try:
            # Create dummy data
            input_ids = torch.randint(0, vocab_size-10, (batch_size, 128)).cuda()
            attention_mask = torch.ones_like(input_ids).cuda()
            labels = input_ids.clone()
            
            # Forward pass
            outputs = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                labels=labels,
                return_thoughts=True,
            )
            
            forward_memory = torch.cuda.memory_allocated()
            logger.info(f"  After forward pass: {forward_memory / 1024**2:.1f} MB")
            
            # Backward pass
            loss = outputs["loss"]
            loss.backward()
            
            backward_memory = torch.cuda.memory_allocated()
            logger.info(f"  After backward pass: {backward_memory / 1024**2:.1f} MB")
            
            # Clear gradients
            model.zero_grad()
            
            # Clear intermediate tensors
            del outputs, loss, input_ids, attention_mask, labels
            torch.cuda.empty_cache()
            
            cleared_memory = torch.cuda.memory_allocated()
            logger.info(f"  After clearing: {cleared_memory / 1024**2:.1f} MB")
            
        except Exception as e:
            logger.error(f"  Memory test failed for batch size {batch_size}: {e}")
            return False
    
    return True

def main():
    """Main debug function."""
    logger.info("Starting backward pass debug...")
    
    # Test 1: Backward pass
    logger.info("\n" + "="*50)
    logger.info("Test 1: Backward pass")
    logger.info("="*50)
    test1_success = test_backward_pass()
    
    # Test 2: Memory usage
    logger.info("\n" + "="*50)
    logger.info("Test 2: Memory usage")
    logger.info("="*50)
    test2_success = test_memory_usage()
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("SUMMARY")
    logger.info("="*50)
    logger.info(f"Test 1 (Backward pass): {'✓' if test1_success else '✗'}")
    logger.info(f"Test 2 (Memory usage): {'✓' if test2_success else '✗'}")
    
    if test1_success:
        logger.info("Backward pass works fine! The issue might be in the full training loop or specific configurations.")
    else:
        logger.error("Backward pass is failing - this is the source of the problem.")

if __name__ == "__main__":
    main()
