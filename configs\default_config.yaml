# Default configuration for ThinkerLLM

# Model Architecture Configuration
model:
  # Main LLM Configuration
  vocab_size: 50257  # GPT-2 vocabulary size
  hidden_size: 768
  num_layers: 8
  num_attention_heads: 8
  intermediate_size: 3072
  max_position_embeddings: 2048
  dropout_prob: 0.1
  layer_norm_eps: 1.0e-5
  
  # ThinkerModule Configuration
  thinker_hidden_size: 768
  thinker_num_layers: 6
  thinker_num_heads: 6
  thinker_intermediate_size: 3072
  thinker_max_length: 512
  thinker_dropout: 0.1
  
  # Decision Mechanism Configuration
  decision_hidden_size: 512
  decision_num_layers: 3
  decision_threshold: 0.5
  decision_features:
    - "input_length"
    - "complexity_score"
    - "question_type"
  
  # Projection Layer Configuration
  projection_type: "adaptive"  # "linear", "adaptive", "cross_attention"
  projection_hidden_size: 512
  projection_num_layers: 2
  
  # General Configuration
  use_flash_attention: true
  gradient_checkpointing: true
  tie_word_embeddings: true

# Training Configuration
training:
  # Training Parameters
  batch_size: 4
  learning_rate: 5.0e-5
  num_epochs: 3
  warmup_steps: 1000
  max_grad_norm: 1.0
  weight_decay: 0.01
  
  # Loss Weights
  llm_loss_weight: 1.0
  thinker_loss_weight: 0.5
  decision_loss_weight: 0.3
  
  # Training Strategy
  joint_training: true
  pretrain_components: false
  freeze_llm: false
  freeze_thinker: false
  
  # Optimization
  optimizer: "adamw"
  scheduler: "cosine"
  gradient_accumulation_steps: 1
  
  # Mixed Precision Training (NEW)
  use_amp: true  # Enable automatic mixed precision for faster training
  mixed_precision_dtype: "bf16"  # "fp16" or "bf16" - bf16 is more stable

  # Logging and Checkpointing
  logging_steps: 100
  eval_steps: 500
  save_steps: 1000
  save_total_limit: 3
  
  # Data
  max_length: 2048
  thinking_ratio: 0.3  # Proportion of samples requiring thinking
  
  # Paths
  output_dir: "./outputs"
  logging_dir: "./logs"
  cache_dir: "./cache"

# Inference Configuration
inference:
  # Generation Parameters
  max_new_tokens: 512
  temperature: 0.7
  top_p: 0.9
  top_k: 50
  do_sample: true
  num_beams: 1
  
  # Thinking Parameters
  show_thinking: true
  thinking_temperature: 0.8
  max_thinking_tokens: 256
  
  # Decision Parameters
  decision_threshold: 0.5
  force_thinking: false
  disable_thinking: false
  
  # Performance
  batch_size: 1
  use_cache: true
  
  # Output Formatting
  include_metadata: true
  format_thoughts: true
  clean_output: true

# Data Configuration
data:
  # Training data paths
  train_data_path: "./data/train.jsonl"
  val_data_path: "./data/val.jsonl"
  test_data_path: "./data/test.jsonl"
  
  # Tokenizer
  tokenizer_name: "gpt2"
  
  # Data processing
  max_length: 2048
  thinking_ratio: 0.3
  include_thoughts: true
  
  # Data loader
  num_workers: 4
  pin_memory: true
  shuffle_train: true

# Environment Configuration
environment:
  # Device settings
  device: "auto"  # "auto", "cpu", "cuda", "cuda:0", etc.
  mixed_precision: true
  compile_model: false  # PyTorch 2.0+ compilation
  
  # Memory optimization
  gradient_checkpointing: false
  use_flash_attention: true
  
  # Reproducibility
  seed: 42
  deterministic: false

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # Weights & Biases
  use_wandb: true
  wandb_project: "thinker-llm"
  wandb_entity: null
  
  # TensorBoard
  use_tensorboard: true
  
  # Console logging
  log_to_console: true
  log_to_file: true

# Evaluation Configuration
evaluation:
  # Metrics to compute
  metrics:
    - "perplexity"
    - "thinking_accuracy"
    - "decision_accuracy"
    - "generation_quality"
  
  # Evaluation frequency
  eval_steps: 500
  eval_during_training: true
  
  # Test sets
  test_sets:
    - name: "reasoning_tasks"
      path: "./data/reasoning_test.jsonl"
      requires_thinking: true
    - name: "factual_qa"
      path: "./data/factual_test.jsonl"
      requires_thinking: false

# Model Variants
variants:
  # Small model for development/testing
  small:
    model:
      hidden_size: 512
      num_layers: 6
      num_attention_heads: 8
      thinker_hidden_size: 768
      thinker_num_layers: 4
    training:
      batch_size: 16
      learning_rate: 1.0e-4
  
  # Large model for production
  large:
    model:
      hidden_size: 1024
      num_layers: 24
      num_attention_heads: 16
      thinker_hidden_size: 1536
      thinker_num_layers: 12
    training:
      batch_size: 4
      learning_rate: 3.0e-5
      gradient_accumulation_steps: 4
