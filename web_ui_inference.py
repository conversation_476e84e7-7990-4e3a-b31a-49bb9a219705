#!/usr/bin/env python3
"""
Web UI for ThinkerLLM Inference with Debug Information
Provides an interactive interface to test the trained model and visualize its thinking process.
"""

import sys
import os
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent))

import torch
import gradio as gr
import json
import time
from datetime import datetime
from transformers import AutoTokenizer
import logging

# Import ThinkerLLM components
from thinker_llm.models import IntegratedThinkerLLM
from thinker_llm.utils.config import ModelConfig
from thinker_llm.inference.generator import ThinkerGenerator

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ThinkerWebUI:
    def __init__(self, model_path=None, config_path="configs/training_config.yaml"):
        """Initialize the web UI with model and tokenizer."""
        self.model = None
        self.tokenizer = None
        self.generator = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Load model if path provided
        if model_path and os.path.exists(model_path):
            self.load_model(model_path)
        else:
            # Initialize with base model for demo
            self.initialize_base_model()
    
    def initialize_base_model(self, config=None):
        """Initialize with base model configuration."""
        try:
            logger.info("Initializing base model...")
            
            # Setup tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained("gpt2")
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Add special tokens
            special_tokens = ["<|thinking|>", "<|/thinking|>", "<|response|>", "<|/response|>"]
            new_tokens = [token for token in special_tokens if token not in self.tokenizer.get_vocab()]
            if new_tokens:
                self.tokenizer.add_tokens(new_tokens)
            
            # Create model
            model_config = config or ModelConfig()
            model_config.vocab_size = len(self.tokenizer)
            model_config.use_flash_attention = False  # Disable for stability
            
            self.model = IntegratedThinkerLLM(model_config)
            self.model.resize_token_embeddings(len(self.tokenizer))
            self.model.to(self.device)
            self.model.eval()
            
            # Create generator
            self.generator = ThinkerGenerator(self.model, self.tokenizer)
            
            logger.info(f"Base model initialized successfully on {self.device}")
            logger.info(f"Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
            
        except Exception as e:
            logger.error(f"Failed to initialize base model: {e}")
            raise
    
    def load_model(self, model_path):
        """Load a trained model from checkpoint."""
        try:
            logger.info(f"Loading model from {model_path}")
            
            # Load checkpoint
            checkpoint = torch.load(model_path, map_location=self.device)
            config_dict = checkpoint.get("model_config", None)
            config = ModelConfig(**config_dict)
            if config is None:
                logger.warning("No model configuration found in checkpoint. Using default configuration.")
                config = ModelConfig()
            
            # Initialize tokenizer and model
            self.initialize_base_model(config)
            
            # Load model state
            if 'model_state_dict' in checkpoint:
                self.model.load_state_dict(checkpoint['model_state_dict'])
                logger.info(f"Loaded model from epoch {checkpoint.get('epoch', 'unknown')}")
            else:
                self.model.load_state_dict(checkpoint)
                logger.info("Loaded model state dict")
            
            self.model.eval()
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            # Fallback to base model
            self.initialize_base_model()
    
    def generate_response(self, prompt, max_length=512, temperature=0.7, top_p=0.9, 
                         thinking_budget=0.3, show_thinking=True, debug_mode=False):
        """Generate response with debug information."""
        if not self.model or not self.tokenizer:
            return "Error: Model not loaded", "", {}
        
        # try:
        start_time = time.time()
        
        # Prepare generation parameters
        generation_params = {
            'max_length': max_length,
            'temperature': temperature,
            'top_p': top_p,
            'thinking_budget': thinking_budget,
            'return_thoughts': show_thinking,
            'debug': debug_mode
        }
        
        # Generate response
        result = self.generator.generate(prompt, **generation_params)
        
        generation_time = time.time() - start_time
        
        # Extract components
        response = result.get('response', '')
        thinking = result.get('thinking', '') if show_thinking else ''
        debug_info = self._format_debug_info(result, generation_time, generation_params)
            
        return response, thinking, debug_info
            
        # except Exception as e:
        #     logger.error(f"Generation failed: {e}")
        #     return f"Error: {str(e)}", "", {"error": str(e)}
    
    def _format_debug_info(self, result, generation_time, params):
        """Format debug information for display."""
        debug_info = {
            "generation_time": f"{generation_time:.2f}s",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "parameters": params,
            "model_info": {
                "device": str(self.device),
                "vocab_size": len(self.tokenizer),
                "model_parameters": f"{sum(p.numel() for p in self.model.parameters()):,}"
            }
        }
        
        # Add token statistics if available
        if 'token_stats' in result:
            debug_info['token_stats'] = result['token_stats']
        
        # Add decision information if available
        if 'decisions' in result:
            debug_info['decisions'] = result['decisions']
        
        # Add attention weights if available
        if 'attention_weights' in result:
            debug_info['attention_info'] = "Available (not displayed for brevity)"
        
        return debug_info

def create_interface(web_ui):
    """Create the Gradio interface."""
    
    with gr.Blocks(title="ThinkerLLM Web UI", theme=gr.themes.Soft()) as interface:
        gr.Markdown("# 🧠 ThinkerLLM Interactive Demo")
        gr.Markdown("Explore the thinking process of ThinkerLLM with real-time inference and debug information.")
        
        with gr.Row():
            with gr.Column(scale=2):
                # Input section
                gr.Markdown("## 💭 Input")
                prompt_input = gr.Textbox(
                    label="Prompt",
                    placeholder="Enter your question or prompt here...",
                    lines=3,
                    value="What is the capital of France and why is it important?"
                )
                
                with gr.Row():
                    generate_btn = gr.Button("🚀 Generate", variant="primary")
                    clear_btn = gr.Button("🗑️ Clear")
                
                # Parameters section
                gr.Markdown("## ⚙️ Generation Parameters")
                with gr.Row():
                    max_length = gr.Slider(
                        minimum=50, maximum=1024, value=512, step=50,
                        label="Max Length"
                    )
                    temperature = gr.Slider(
                        minimum=0.1, maximum=2.0, value=0.7, step=0.1,
                        label="Temperature"
                    )
                
                with gr.Row():
                    top_p = gr.Slider(
                        minimum=0.1, maximum=1.0, value=0.9, step=0.05,
                        label="Top-p"
                    )
                    thinking_budget = gr.Slider(
                        minimum=0.1, maximum=0.8, value=0.3, step=0.05,
                        label="Thinking Budget"
                    )
                
                with gr.Row():
                    show_thinking = gr.Checkbox(label="Show Thinking Process", value=True)
                    debug_mode = gr.Checkbox(label="Debug Mode", value=False)
            
            with gr.Column(scale=3):
                # Output section
                gr.Markdown("## 🎯 Response")
                response_output = gr.Textbox(
                    label="Generated Response",
                    lines=8,
                    interactive=False
                )
                
                gr.Markdown("## 🤔 Thinking Process")
                thinking_output = gr.Textbox(
                    label="Model's Internal Thoughts",
                    lines=6,
                    interactive=False,
                    visible=True
                )
        
        # Debug information section
        with gr.Row():
            with gr.Column():
                gr.Markdown("## 🔍 Debug Information")
                debug_output = gr.JSON(label="Debug Info")
        
        # Model management section
        with gr.Row():
            with gr.Column():
                gr.Markdown("## 🔧 Model Management")
                model_path_input = gr.Textbox(
                    label="Model Checkpoint Path",
                    placeholder="./outputs/checkpoint_step_1000.pt",
                    value="./outputs/checkpoint_step_1000.pt"
                )
                load_model_btn = gr.Button("📁 Load Model")
                model_status = gr.Textbox(label="Model Status", interactive=False)
        
        # Event handlers
        def generate_wrapper(*args):
            return web_ui.generate_response(*args)
        
        def load_model_wrapper(model_path):
            try:
                if os.path.exists(model_path):
                    web_ui.load_model(model_path)
                    return f"✅ Model loaded successfully from {model_path}"
                else:
                    return f"❌ Model file not found: {model_path}"
            except Exception as e:
                return f"❌ Failed to load model: {str(e)}"
        
        def clear_inputs():
            return "", "", "", {}
        
        # Connect events
        generate_btn.click(
            fn=generate_wrapper,
            inputs=[prompt_input, max_length, temperature, top_p, thinking_budget, show_thinking, debug_mode],
            outputs=[response_output, thinking_output, debug_output]
        )
        
        clear_btn.click(
            fn=clear_inputs,
            outputs=[prompt_input, response_output, thinking_output, debug_output]
        )
        
        load_model_btn.click(
            fn=load_model_wrapper,
            inputs=[model_path_input],
            outputs=[model_status]
        )
        
        # Update thinking visibility based on checkbox
        show_thinking.change(
            fn=lambda x: gr.update(visible=x),
            inputs=[show_thinking],
            outputs=[thinking_output]
        )
    
    return interface

def main():
    """Main function to launch the web UI."""
    import argparse
    
    parser = argparse.ArgumentParser(description="ThinkerLLM Web UI")
    parser.add_argument("--model-path", type=str, help="Path to trained model checkpoint")
    parser.add_argument("--host", type=str, default="127.0.0.1", help="Host to bind to")
    parser.add_argument("--port", type=int, default=7860, help="Port to bind to")
    parser.add_argument("--share", action="store_true", help="Create public link")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    
    args = parser.parse_args()
    
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Initialize web UI
    logger.info("Initializing ThinkerLLM Web UI...")
    web_ui = ThinkerWebUI(model_path=args.model_path)
    
    # Create interface
    interface = create_interface(web_ui)
    
    # Launch
    logger.info(f"Launching web UI on {args.host}:{args.port}")
    interface.launch(
        server_name=args.host,
        server_port=args.port,
        share=args.share,
        show_error=True
    )

if __name__ == "__main__":
    main()
