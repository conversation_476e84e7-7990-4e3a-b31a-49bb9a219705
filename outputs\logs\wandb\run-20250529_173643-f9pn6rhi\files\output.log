INFO:__main__:Starting training...
INFO:thinker_llm.training.trainer:Starting training...
INFO:thinker_llm.training.trainer:Total epochs: 1
INFO:thinker_llm.training.trainer:Total steps: 40
Epoch 1/1:   0%|                                                                                           | 0/40 [00:00<?, ?it/s, loss=4.1303, lr=0.00e+00]INFO:thinker_llm.training.trainer:train/loss: 4.1303
INFO:thinker_llm.training.trainer:train/learning_rate: 0.0000
INFO:thinker_llm.training.trainer:train/global_step: 0.0000
INFO:thinker_llm.training.trainer:Checkpoint saved: ./outputs\checkpoint_step_0.pt
ERROR:__main__:Training failed with error: CUDA error: device-side assert triggered                                                                         
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

Traceback (most recent call last):
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\scripts\train_thinker_llm.py", line 260, in <module>
    main()
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\scripts\train_thinker_llm.py", line 224, in main
    trainer.train()
  File "d:\vlinhd11\projects\lcms_llm\moe-llm\geneticai\v4\thinker_llm\training\trainer.py", line 297, in train
    epoch_metrics = self.train_epoch()
                    ^^^^^^^^^^^^^^^^^^
  File "d:\vlinhd11\projects\lcms_llm\moe-llm\geneticai\v4\thinker_llm\training\trainer.py", line 150, in train_epoch
    loss_dict = self.loss_fn(
                ^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\vlinhd11\projects\lcms_llm\moe-llm\geneticai\v4\thinker_llm\training\losses.py", line 388, in forward
    llm_losses = self.llm_loss(
                 ^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\vlinhd11\projects\lcms_llm\moe-llm\geneticai\v4\thinker_llm\training\losses.py", line 206, in forward
    hidden_norm = F.normalize(hidden_states.mean(dim=1), dim=-1)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\functional.py", line 5486, in normalize
    denom = input.norm(p, dim, keepdim=True).clamp_min(eps).expand_as(input)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\_tensor.py", line 894, in norm
    return torch.norm(self, p, dim, keepdim, dtype=dtype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\functional.py", line 1849, in norm
    return torch.linalg.vector_norm(input, _p, _dim, keepdim, dtype=dtype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
RuntimeError: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.
