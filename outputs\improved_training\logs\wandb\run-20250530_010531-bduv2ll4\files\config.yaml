_wandb:
    value:
        cli_version: 0.19.8
        m: []
        python_version: 3.12.9
        t:
            "1":
                - 1
                - 5
                - 11
                - 49
                - 53
                - 55
                - 71
                - 105
            "2":
                - 1
                - 5
                - 11
                - 49
                - 53
                - 55
                - 71
                - 105
            "3":
                - 16
                - 23
                - 55
                - 61
            "4": 3.12.9
            "5": 0.19.8
            "6": 4.51.3
            "8":
                - 3
                - 5
            "12": 0.19.8
            "13": windows-amd64
batch_size:
    value: 4
cache_dir:
    value: ./cache
decision_loss_weight:
    value: 0.6
device:
    value: cuda
eval_steps:
    value: 100
freeze_llm:
    value: false
freeze_thinker:
    value: false
gradient_accumulation_steps:
    value: 4
joint_training:
    value: true
learning_rate:
    value: 2e-05
llm_loss_weight:
    value: 1
logging_dir:
    value: ./outputs/improved_training/logs
logging_steps:
    value: 25
max_grad_norm:
    value: 1
max_length:
    value: 512
mixed_precision_dtype:
    value: bfloat16
num_epochs:
    value: 5
optimizer:
    value: adamw
output_dir:
    value: ./outputs/improved_training
pretrain_components:
    value: false
save_steps:
    value: 200
save_total_limit:
    value: 5
scheduler:
    value: cosine
thinker_loss_weight:
    value: 0.8
thinking_ratio:
    value: 0.8
use_amp:
    value: true
warmup_steps:
    value: 100
weight_decay:
    value: 0.01
