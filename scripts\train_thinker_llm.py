"""
Complete training script for ThinkerLLM.

This script handles the entire training process from data loading to model saving.
"""
import sys
import os
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent.parent))
import torch
import argparse
import logging
from transformers import AutoTokenizer
import yaml

# Import ThinkerLLM components
from thinker_llm.models import IntegratedThinkerLLM
from thinker_llm.utils.config import ModelConfig, TrainingConfig

from thinker_llm.training.trainer import ThinkerLLMTrainer
from thinker_llm.utils.model_utils import save_model, get_model_size_info

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def load_config(config_path: str) -> dict:
    """Load configuration from YAML file."""
    with open(config_path, 'r') as f:
        return yaml.safe_load(f)


def setup_model_and_tokenizer(model_config: ModelConfig, tokenizer_name: str, data_path: str):
    """Setup model and tokenizer."""
    logger.info("Setting up model and tokenizer...")

    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    # Add special tokens that will be used in the dataset
    special_tokens = ["<|thinking|>", "<|/thinking|>", "<|response|>", "<|/response|>"]
    new_tokens = []
    for token in special_tokens:
        if token not in tokenizer.get_vocab():
            new_tokens.append(token)

    if new_tokens:
        num_added = tokenizer.add_tokens(new_tokens)
        logger.info(f"Added {num_added} special tokens to tokenizer")

    # Update vocab size in config to match tokenizer BEFORE creating model
    final_vocab_size = len(tokenizer)
    needs_resize = model_config.vocab_size != final_vocab_size

    if needs_resize:
        logger.info(f"Updating vocab size from {model_config.vocab_size} to {final_vocab_size}")
        model_config.vocab_size = final_vocab_size

    # Ensure FlashAttention is disabled to avoid dtype issues
    model_config.use_flash_attention = False

    # Create model with correct vocabulary size from the beginning
    model = IntegratedThinkerLLM(model_config)

    # Explicitly resize embeddings to ensure they match the tokenizer vocab size
    if needs_resize:
        logger.info(f"Resizing model embeddings to match tokenizer vocab size: {final_vocab_size}")
        model.resize_token_embeddings(final_vocab_size)

    # Print model info
    model_info = get_model_size_info(model)
    logger.info(f"Model created:")
    logger.info(f"  - Total parameters: {model_info['total_parameters']:,}")
    logger.info(f"  - Trainable parameters: {model_info['trainable_parameters']:,}")
    logger.info(f"  - Model size: {model_info['model_size_mb']:.1f} MB")
    logger.info(f"  - Vocabulary size: {model_config.vocab_size}")

    if 'component_parameters' in model_info:
        logger.info("  - Component breakdown:")
        for component, params in model_info['component_parameters'].items():
            logger.info(f"    - {component}: {params:,} parameters")

    return model, tokenizer


def prepare_data(data_config: dict, tokenizer: AutoTokenizer, training_config: TrainingConfig):
    """Prepare training and validation datasets."""
    logger.info("Preparing datasets...")

    # Create dataset directly with the tokenizer instance
    from thinker_llm.training.data_loader import ThinkerDataset, ThinkerDataLoader

    # Prepare training data
    train_dataset = ThinkerDataset(
        data_path=data_config['train_data_path'],
        tokenizer=tokenizer,  # Use the same tokenizer instance
        max_length=training_config.max_length,
        thinking_ratio=training_config.thinking_ratio,
        split="train",
    )

    train_dataloader = ThinkerDataLoader(
        dataset=train_dataset,
        batch_size=training_config.batch_size,
        shuffle=True,
    )

    logger.info(f"Training dataset: {len(train_dataset)} samples")

    # Prepare validation data if available
    val_dataloader = None
    if data_config.get('val_data_path') and Path(data_config['val_data_path']).exists():
        val_dataset = ThinkerDataset(
            data_path=data_config['val_data_path'],
            tokenizer=tokenizer,  # Use the same tokenizer instance
            max_length=training_config.max_length,
            thinking_ratio=training_config.thinking_ratio,
            split="train",  # Use same format
        )

        val_dataloader = ThinkerDataLoader(
            dataset=val_dataset,
            batch_size=training_config.batch_size,
            shuffle=False,
        )
        logger.info(f"Validation dataset: {len(val_dataset)} samples")
    else:
        logger.info("No validation data provided")

    return train_dataloader, val_dataloader


def main():
    parser = argparse.ArgumentParser(description="Train ThinkerLLM")
    parser.add_argument("--config", required=True, help="Path to configuration YAML file")
    parser.add_argument("--train-data", required=True, help="Path to training data JSONL file")
    parser.add_argument("--val-data", help="Path to validation data JSONL file")
    parser.add_argument("--output-dir", default="./outputs", help="Output directory for model and logs")
    parser.add_argument("--model-name", default="thinker_llm", help="Name for the saved model")
    parser.add_argument("--resume", help="Path to checkpoint to resume from")
    parser.add_argument("--dry-run", action="store_true", help="Run without actual training (for testing)")

    # Training strategy options
    parser.add_argument("--strategy", choices=["joint", "frozen_llm", "frozen_thinker", "frozen_decision"],
                       default="joint", help="Training strategy")

    # Override options
    parser.add_argument("--batch-size", type=int, help="Override batch size")
    parser.add_argument("--learning-rate", type=float, help="Override learning rate")
    parser.add_argument("--num-epochs", type=int, help="Override number of epochs")
    parser.add_argument("--thinking-ratio", type=float, help="Override thinking ratio")

    args = parser.parse_args()

    # Load configuration
    logger.info(f"Loading configuration from {args.config}")
    config = load_config(args.config)

    # Update data paths
    config['data']['train_data_path'] = args.train_data
    if args.val_data:
        config['data']['val_data_path'] = args.val_data

    # Update output directory
    config['training']['output_dir'] = args.output_dir
    config['training']['logging_dir'] = os.path.join(args.output_dir, "logs")

    # Apply overrides
    if args.batch_size:
        config['training']['batch_size'] = args.batch_size
    if args.learning_rate:
        config['training']['learning_rate'] = args.learning_rate
    if args.num_epochs:
        config['training']['num_epochs'] = args.num_epochs
    if args.thinking_ratio:
        config['training']['thinking_ratio'] = args.thinking_ratio

    # Create config objects
    model_config = ModelConfig(**config['model'])
    training_config = TrainingConfig(**config['training'])

    logger.info("Configuration loaded:")
    logger.info(f"  - Training strategy: {args.strategy}")
    logger.info(f"  - Batch size: {training_config.batch_size}")
    logger.info(f"  - Learning rate: {training_config.learning_rate}")
    logger.info(f"  - Epochs: {training_config.num_epochs}")
    logger.info(f"  - Thinking ratio: {training_config.thinking_ratio}")

    # Setup model and tokenizer
    model, tokenizer = setup_model_and_tokenizer(
        model_config,
        config['data']['tokenizer_name'],
        args.train_data
    )
    if config['training']['mixed_precision_dtype'] == "bf16":
        config['training']['use_amp'] = False
        logger.info("Mixed precision disabled for bf16")
        model.to(torch.bfloat16)
    elif config['training']['mixed_precision_dtype'] == "fp16":
        config['training']['use_amp'] = True
        logger.info("Mixed precision enabled for fp16")
        model.to(torch.float16)
    else:
        raise ValueError(f"Invalid mixed precision dtype: {config['training']['mixed_precision_dtype']}")

    # Set training strategy
    model.set_training_mode(args.strategy)
    logger.info(f"Training mode set to: {args.strategy}")

    # Prepare data
    train_dataloader, val_dataloader = prepare_data(
        config['data'],
        tokenizer,
        training_config
    )

    # Create trainer (model already has correct vocab size)
    logger.info("Setting up trainer...")
    trainer = ThinkerLLMTrainer(
        model=model,
        train_dataloader=train_dataloader.get_dataloader(),
        val_dataloader=val_dataloader.get_dataloader() if val_dataloader else None,
        config=training_config,
        model_config=model.config,  # Use model's config which has the correct vocab size
    )

    # Resume from checkpoint if specified
    if args.resume:
        logger.info(f"Resuming from checkpoint: {args.resume}")
        trainer.load_checkpoint(args.resume)

    # Dry run check
    if args.dry_run:
        logger.info("Dry run mode - skipping actual training")
        logger.info("Setup completed successfully!")
        return

    # Start training
    logger.info("Starting training...")
    try:
        trainer.train()
        logger.info("Training completed successfully!")

        # Save final model
        model_save_path = os.path.join(args.output_dir, args.model_name)
        logger.info(f"Saving model to {model_save_path}")

        save_model(
            model=model,
            tokenizer=tokenizer,
            save_path=model_save_path,
            model_config=model_config,
            training_config=training_config,
            additional_info={
                "training_strategy": args.strategy,
                "final_step": trainer.global_step,
                "final_epoch": trainer.epoch,
                "best_val_loss": trainer.best_val_loss,
            }
        )

        logger.info("Model saved successfully!")

    except KeyboardInterrupt:
        logger.info("Training interrupted by user")
        # Save checkpoint
        checkpoint_path = os.path.join(args.output_dir, "interrupted_checkpoint.pt")
        trainer.save_checkpoint()
        logger.info(f"Checkpoint saved to {checkpoint_path}")

    except Exception as e:
        logger.error(f"Training failed with error: {e}")
        raise


if __name__ == "__main__":
    main()
