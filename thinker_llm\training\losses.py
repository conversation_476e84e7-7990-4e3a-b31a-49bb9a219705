"""
Loss functions for ThinkerLLM training.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Dict, Any, Tuple
import math


class ThinkerLoss(nn.Module):
    """
    Loss function for ThinkerModule training.
    Combines thought generation loss with reasoning quality metrics.
    """

    def __init__(
        self,
        vocab_size: int,
        thought_weight: float = 1.0,
        reasoning_weight: float = 0.5,
        consistency_weight: float = 0.3,
        label_smoothing: float = 0.1,
    ):
        super().__init__()
        self.vocab_size = vocab_size
        self.thought_weight = thought_weight
        self.reasoning_weight = reasoning_weight
        self.consistency_weight = consistency_weight

        # Loss functions
        self.thought_loss_fn = nn.CrossEntropyLoss(
            label_smoothing=label_smoothing,
            ignore_index=-100,
        )
        self.mse_loss = nn.MSELoss()
        self.cosine_loss = nn.CosineEmbeddingLoss()

    def update_vocab_size(self, new_vocab_size: int):
        """Update vocabulary size."""
        self.vocab_size = new_vocab_size

    def forward(
        self,
        thought_logits: torch.Tensor,
        thought_labels: torch.Tensor,
        reasoning_states: torch.Tensor,
        target_reasoning: Optional[torch.Tensor] = None,
        attention_mask: Optional[torch.Tensor] = None,
    ) -> Dict[str, torch.Tensor]:
        """
        Compute ThinkerModule loss.

        Args:
            thought_logits: Thought generation logits (batch_size, seq_len, vocab_size)
            thought_labels: Target thought tokens (batch_size, seq_len)
            reasoning_states: Generated reasoning states (batch_size, seq_len, hidden_size)
            target_reasoning: Optional target reasoning states
            attention_mask: Optional attention mask

        Returns:
            Dictionary containing loss components
        """
        losses = {}

        # Thought generation loss
        if thought_labels is not None:
            # Ensure logits and labels have compatible dimensions
            batch_size = thought_logits.size(0)
            logits_seq_len = thought_logits.size(1)
            labels_seq_len = thought_labels.size(1)

            # Handle sequence length mismatch
            if logits_seq_len != labels_seq_len:
                # Truncate to the shorter length
                min_len = min(logits_seq_len, labels_seq_len)
                thought_logits = thought_logits[:, :min_len, :]
                thought_labels = thought_labels[:, :min_len]

            # Shift tokens for autoregressive loss
            if thought_logits.size(1) > 1:  # Need at least 2 tokens for shifting
                shift_logits = thought_logits[..., :-1, :].contiguous()
                shift_labels = thought_labels[..., 1:].contiguous()

                # Flatten for loss computation
                flat_logits = shift_logits.view(-1, shift_logits.size(-1))
                flat_labels = shift_labels.view(-1)

                # Ensure labels are in valid range with same safety checks as LLMLoss
                valid_mask = flat_labels != -100

                # Only clamp valid labels to prevent out-of-bounds access
                if valid_mask.any():
                    valid_labels = flat_labels[valid_mask]
                    # Clamp valid labels to vocabulary range
                    clamped_valid_labels = torch.clamp(valid_labels, min=0, max=self.vocab_size - 1)
                    flat_labels[valid_mask] = clamped_valid_labels

                    # Log warning if any labels were out of bounds
                    if (valid_labels >= self.vocab_size).any() or (valid_labels < 0).any():
                        print(f"Warning (ThinkerLoss): Found labels out of bounds. Max label: {valid_labels.max().item()}, "
                              f"Min label: {valid_labels.min().item()}, Vocab size: {self.vocab_size}")

                # Additional safety check for logits dimension
                if flat_logits.size(-1) != self.vocab_size:
                    print(f"Warning (ThinkerLoss): Logits dimension ({flat_logits.size(-1)}) doesn't match vocab size ({self.vocab_size})")
                    # Adjust logits to match vocab size if needed
                    if flat_logits.size(-1) > self.vocab_size:
                        flat_logits = flat_logits[..., :self.vocab_size]
                    else:
                        # Pad with zeros if logits are smaller
                        padding_size = self.vocab_size - flat_logits.size(-1)
                        padding = torch.zeros(flat_logits.size(0), padding_size, device=flat_logits.device, dtype=flat_logits.dtype)
                        flat_logits = torch.cat([flat_logits, padding], dim=-1)

                thought_loss = self.thought_loss_fn(flat_logits, flat_labels)
                losses["thought_loss"] = thought_loss
            else:
                # If sequence is too short, create a dummy loss
                losses["thought_loss"] = torch.tensor(0.0, device=thought_logits.device)

        # Reasoning state consistency loss
        if reasoning_states is not None:
            # Self-consistency: reasoning states should be stable across similar inputs
            batch_size, seq_len, hidden_size = reasoning_states.shape

            # Compute pairwise consistency within batch
            if batch_size > 1:
                # Normalize reasoning states
                normalized_states = F.normalize(reasoning_states.mean(dim=1), dim=-1)

                # Compute similarity matrix
                similarity_matrix = torch.mm(normalized_states, normalized_states.t())

                # Encourage diversity (avoid collapse)
                diversity_target = torch.eye(batch_size, device=reasoning_states.device) * 0.1
                diversity_loss = self.mse_loss(similarity_matrix, diversity_target)
                losses["diversity_loss"] = diversity_loss

        # Target reasoning loss (if available)
        if target_reasoning is not None and reasoning_states is not None:
            # Direct supervision on reasoning states
            if attention_mask is not None:
                # Mask out padding positions
                mask = attention_mask.unsqueeze(-1).expand_as(reasoning_states)
                masked_reasoning = reasoning_states * mask
                masked_target = target_reasoning * mask

                # Compute masked MSE loss
                diff = (masked_reasoning - masked_target) ** 2
                reasoning_loss = diff.sum() / mask.sum()
            else:
                reasoning_loss = self.mse_loss(reasoning_states, target_reasoning)

            losses["reasoning_loss"] = reasoning_loss

        # Combine losses
        total_loss = torch.tensor(0.0, device=thought_logits.device)

        if "thought_loss" in losses:
            total_loss += self.thought_weight * losses["thought_loss"]

        if "reasoning_loss" in losses:
            total_loss += self.reasoning_weight * losses["reasoning_loss"]

        if "diversity_loss" in losses:
            total_loss += self.consistency_weight * losses["diversity_loss"]

        losses["total_loss"] = total_loss

        return losses


class LLMLoss(nn.Module):
    """
    Loss function for MainLLM training.
    Standard language modeling loss with optional reasoning integration penalty.
    """

    def __init__(
        self,
        vocab_size: int,
        label_smoothing: float = 0.1,
        integration_weight: float = 0.2,
    ):
        super().__init__()
        self.vocab_size = vocab_size
        self.integration_weight = integration_weight

        self.lm_loss_fn = nn.CrossEntropyLoss(
            label_smoothing=label_smoothing,
            ignore_index=-100,
        )
        self.mse_loss = nn.MSELoss()

    def update_vocab_size(self, new_vocab_size: int):
        """Update vocabulary size."""
        self.vocab_size = new_vocab_size

    def forward(
        self,
        logits: torch.Tensor,
        labels: torch.Tensor,
        hidden_states: Optional[torch.Tensor] = None,
        reasoning_states: Optional[torch.Tensor] = None,
        attention_mask: Optional[torch.Tensor] = None,
    ) -> Dict[str, torch.Tensor]:
        """
        Compute MainLLM loss.

        Args:
            logits: Language model logits (batch_size, seq_len, vocab_size)
            labels: Target tokens (batch_size, seq_len)
            hidden_states: LLM hidden states
            reasoning_states: Optional reasoning states from ThinkerModule
            attention_mask: Optional attention mask

        Returns:
            Dictionary containing loss components
        """
        losses = {}

        # Standard language modeling loss
        shift_logits = logits[..., :-1, :].contiguous()
        shift_labels = labels[..., 1:].contiguous()

        flat_logits = shift_logits.view(-1, shift_logits.size(-1))
        flat_labels = shift_labels.view(-1)

        # Ensure labels are in valid range (CrossEntropyLoss handles -100 automatically)
        # But let's be extra safe and clamp any other invalid values
        # Create a mask for valid labels (not -100)
        valid_mask = flat_labels != -100

        # Only clamp valid labels to prevent out-of-bounds access
        if valid_mask.any():
            valid_labels = flat_labels[valid_mask]
            # Clamp valid labels to vocabulary range
            clamped_valid_labels = torch.clamp(valid_labels, min=0, max=self.vocab_size - 1)
            flat_labels[valid_mask] = clamped_valid_labels

            # Log warning if any labels were out of bounds
            if (valid_labels >= self.vocab_size).any() or (valid_labels < 0).any():
                print(f"Warning: Found labels out of bounds. Max label: {valid_labels.max().item()}, "
                      f"Min label: {valid_labels.min().item()}, Vocab size: {self.vocab_size}")

        # Additional safety check for logits dimension
        if flat_logits.size(-1) != self.vocab_size:
            print(f"Warning: Logits dimension ({flat_logits.size(-1)}) doesn't match vocab size ({self.vocab_size})")
            # Adjust logits to match vocab size if needed
            if flat_logits.size(-1) > self.vocab_size:
                flat_logits = flat_logits[..., :self.vocab_size]
            else:
                # Pad with zeros if logits are smaller
                padding_size = self.vocab_size - flat_logits.size(-1)
                padding = torch.zeros(flat_logits.size(0), padding_size, device=flat_logits.device, dtype=flat_logits.dtype)
                flat_logits = torch.cat([flat_logits, padding], dim=-1)

        lm_loss = self.lm_loss_fn(flat_logits, flat_labels)
        losses["lm_loss"] = lm_loss

        # Integration quality loss (if reasoning states are used)
        if reasoning_states is not None and hidden_states is not None:
            try:
                # Encourage effective use of reasoning states
                # Measure how much the hidden states change when reasoning is available

                batch_size, seq_len, hidden_size = hidden_states.shape

                # Compute attention between hidden states and reasoning states
                hidden_mean = hidden_states.mean(dim=1)
                reasoning_mean = reasoning_states.mean(dim=1)

                # Handle dimension mismatch by projecting to same size
                if hidden_mean.size(-1) != reasoning_mean.size(-1):
                    min_dim = min(hidden_mean.size(-1), reasoning_mean.size(-1))
                    hidden_mean = hidden_mean[..., :min_dim]
                    reasoning_mean = reasoning_mean[..., :min_dim]

                # Check for valid tensors before normalization
                if torch.isfinite(hidden_mean).all() and torch.isfinite(reasoning_mean).all():
                    # Add small epsilon to prevent division by zero
                    eps = 1e-8
                    hidden_norm = F.normalize(hidden_mean + eps, dim=-1)
                    reasoning_norm = F.normalize(reasoning_mean + eps, dim=-1)

                    # Cosine similarity as integration measure
                    integration_score = F.cosine_similarity(hidden_norm, reasoning_norm, dim=-1)

                    # Check for valid integration score
                    if torch.isfinite(integration_score).all():
                        # Encourage moderate integration (not too high, not too low)
                        target_integration = torch.full_like(integration_score, 0.5)
                        integration_loss = self.mse_loss(integration_score, target_integration)
                        losses["integration_loss"] = integration_loss
                    else:
                        # Skip integration loss if invalid
                        losses["integration_loss"] = torch.tensor(0.0, device=hidden_states.device)
                else:
                    # Skip integration loss if invalid inputs
                    losses["integration_loss"] = torch.tensor(0.0, device=hidden_states.device)

            except Exception as e:
                # Fallback: skip integration loss if any error occurs
                losses["integration_loss"] = torch.tensor(0.0, device=hidden_states.device)

        # Combine losses
        total_loss = losses["lm_loss"]

        if "integration_loss" in losses:
            total_loss += self.integration_weight * losses["integration_loss"]

        losses["total_loss"] = total_loss

        return losses


class DecisionLoss(nn.Module):
    """
    Loss function for DecisionMechanism training.
    Combines decision accuracy with calibration and efficiency metrics.
    """

    def __init__(
        self,
        decision_weight: float = 1.0,
        calibration_weight: float = 0.3,
        efficiency_weight: float = 0.2,
    ):
        super().__init__()
        self.decision_weight = decision_weight
        self.calibration_weight = calibration_weight
        self.efficiency_weight = efficiency_weight

        # Use MSELoss instead of BCELoss for mixed precision compatibility
        # Since thinking_probability is already sigmoid-activated, we can treat it as a regression problem
        self.mse_loss = nn.MSELoss()

    def forward(
        self,
        thinking_probability: torch.Tensor,
        thinking_decision: torch.Tensor,
        decision_labels: torch.Tensor,
        question_type: Optional[torch.Tensor] = None,
        question_type_labels: Optional[torch.Tensor] = None,
    ) -> Dict[str, torch.Tensor]:
        """
        Compute DecisionMechanism loss.

        Args:
            thinking_probability: Predicted thinking probabilities
            thinking_decision: Binary thinking decisions
            decision_labels: True thinking requirements
            question_type: Predicted question type probabilities
            question_type_labels: True question type labels

        Returns:
            Dictionary containing loss components
        """
        losses = {}

        # Ensure all tensors are on the same device
        device = thinking_probability.device
        decision_labels = decision_labels.to(device)
        thinking_decision = thinking_decision.to(device)

        # Decision accuracy loss - using MSE instead of BCE for mixed precision compatibility
        # Since thinking_probability is already sigmoid-activated [0,1], this works as regression
        decision_loss = self.mse_loss(thinking_probability, decision_labels.float())
        losses["decision_loss"] = decision_loss

        # Question type classification loss (if available)
        if question_type is not None and question_type_labels is not None:
            type_loss = F.cross_entropy(question_type, question_type_labels)
            losses["type_loss"] = type_loss

        # Calibration loss - probability should match actual accuracy
        with torch.no_grad():
            # Compute accuracy in bins
            prob_bins = torch.linspace(0, 1, 11, device=device)
            calibration_loss = torch.tensor(0.0, device=device)

            for i in range(len(prob_bins) - 1):
                bin_mask = (thinking_probability >= prob_bins[i]) & (thinking_probability < prob_bins[i + 1])
                if bin_mask.sum() > 0:
                    bin_accuracy = decision_labels[bin_mask].float().mean()
                    bin_confidence = thinking_probability[bin_mask].mean()
                    calibration_loss += (bin_accuracy - bin_confidence) ** 2

            calibration_loss /= (len(prob_bins) - 1)
            losses["calibration_loss"] = calibration_loss

        # Efficiency loss - encourage balanced thinking ratio
        thinking_ratio = thinking_decision.mean()
        target_ratio = torch.tensor(0.3, device=device)  # Target 30% thinking
        efficiency_loss = (thinking_ratio - target_ratio) ** 2
        losses["efficiency_loss"] = efficiency_loss

        # Combine losses
        total_loss = self.decision_weight * losses["decision_loss"]

        if "type_loss" in losses:
            total_loss += 0.5 * losses["type_loss"]

        total_loss += self.calibration_weight * losses["calibration_loss"]
        total_loss += self.efficiency_weight * losses["efficiency_loss"]

        losses["total_loss"] = total_loss

        return losses


class CombinedLoss(nn.Module):
    """
    Combined loss function for joint training of all ThinkerLLM components.
    """

    def __init__(
        self,
        vocab_size: int,
        llm_weight: float = 1.0,
        thinker_weight: float = 0.5,
        decision_weight: float = 0.3,
        consistency_weight: float = 0.2,
        label_smoothing: float = 0.1,
    ):
        super().__init__()
        self.llm_weight = llm_weight
        self.thinker_weight = thinker_weight
        self.decision_weight = decision_weight
        self.consistency_weight = consistency_weight

        # Individual loss functions
        self.thinker_loss = ThinkerLoss(
            vocab_size=vocab_size,
            label_smoothing=label_smoothing,
        )
        self.llm_loss = LLMLoss(
            vocab_size=vocab_size,
            label_smoothing=label_smoothing,
        )
        self.decision_loss = DecisionLoss()

    def update_vocab_size(self, new_vocab_size: int):
        """Update vocabulary size for all component losses."""
        self.thinker_loss.update_vocab_size(new_vocab_size)
        self.llm_loss.update_vocab_size(new_vocab_size)

    def forward(
        self,
        model_outputs: Dict[str, torch.Tensor],
        labels: torch.Tensor,
        thinking_labels: Optional[torch.Tensor] = None,
        decision_labels: Optional[torch.Tensor] = None,
        question_type_labels: Optional[torch.Tensor] = None,
    ) -> Dict[str, torch.Tensor]:
        """
        Compute combined loss for all components.

        Args:
            model_outputs: Outputs from IntegratedThinkerLLM
            labels: Target tokens for main LLM
            thinking_labels: Target tokens for thinking
            decision_labels: Target thinking decisions
            question_type_labels: Target question types

        Returns:
            Dictionary containing all loss components
        """
        all_losses = {}

        # LLM loss
        llm_losses = self.llm_loss(
            logits=model_outputs["logits"],
            labels=labels,
            hidden_states=model_outputs.get("hidden_states"),
            reasoning_states=model_outputs.get("reasoning_states"),
        )

        for key, value in llm_losses.items():
            all_losses[f"llm_{key}"] = value

        # Thinker loss (if thinking was applied)
        if "thinker_outputs" in model_outputs and thinking_labels is not None:
            thinker_outputs = model_outputs["thinker_outputs"]
            thinker_losses = self.thinker_loss(
                thought_logits=thinker_outputs["thought_logits"],
                thought_labels=thinking_labels,
                reasoning_states=thinker_outputs["reasoning_states"],
            )

            for key, value in thinker_losses.items():
                all_losses[f"thinker_{key}"] = value

        # Decision loss
        if decision_labels is not None:
            decision_info = model_outputs["decision_info"]
            decision_losses = self.decision_loss(
                thinking_probability=decision_info["thinking_probability"],
                thinking_decision=decision_info["thinking_decision"],
                decision_labels=decision_labels,
                question_type=decision_info.get("question_type"),
                question_type_labels=question_type_labels,
            )

            for key, value in decision_losses.items():
                all_losses[f"decision_{key}"] = value

        # Cross-component consistency loss
        if model_outputs.get("reasoning_states") is not None:
            try:
                # Encourage consistency between decision and actual thinking benefit
                should_think = model_outputs["should_think_mask"].float()
                thinking_prob = model_outputs["decision_info"]["thinking_probability"]

                # Ensure same device
                device = thinking_prob.device
                should_think = should_think.to(device)

                # Check for valid tensors
                if torch.isfinite(thinking_prob).all() and torch.isfinite(should_think).all():
                    consistency_loss = F.mse_loss(thinking_prob, should_think)
                    all_losses["consistency_loss"] = consistency_loss
                else:
                    # Skip consistency loss if invalid
                    all_losses["consistency_loss"] = torch.tensor(0.0, device=device)

            except Exception as e:
                # Fallback: skip consistency loss if any error occurs
                all_losses["consistency_loss"] = torch.tensor(0.0, device=labels.device)

        # Combine all losses
        total_loss = torch.tensor(0.0, device=labels.device)

        if "llm_total_loss" in all_losses:
            total_loss += self.llm_weight * all_losses["llm_total_loss"]

        if "thinker_total_loss" in all_losses:
            total_loss += self.thinker_weight * all_losses["thinker_total_loss"]

        if "decision_total_loss" in all_losses:
            total_loss += self.decision_weight * all_losses["decision_total_loss"]

        if "consistency_loss" in all_losses:
            total_loss += self.consistency_weight * all_losses["consistency_loss"]

        all_losses["total_loss"] = total_loss

        return all_losses
