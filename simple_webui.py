#!/usr/bin/env python3
"""
Simple Web UI for ThinkerLLM - Minimal version for testing
"""

import sys
import os
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent))

import torch
import gradio as gr
import logging
from transformers import AutoTokenizer

# Import ThinkerLLM components
from thinker_llm.models import IntegratedThinkerLLM
from thinker_llm.utils.config import ModelConfig

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleThinkerUI:
    def __init__(self):
        """Initialize the simple UI."""
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = None
        self.tokenizer = None
        self.initialize_model()
    
    def initialize_model(self):
        """Initialize model and tokenizer."""
        try:
            logger.info("Initializing ThinkerLLM...")
            
            # Setup tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained("gpt2")
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Add special tokens
            special_tokens = ["<|thinking|>", "<|/thinking|>", "<|response|>", "<|/response|>"]
            new_tokens = [token for token in special_tokens if token not in self.tokenizer.get_vocab()]
            if new_tokens:
                self.tokenizer.add_tokens(new_tokens)
            
            # Create model
            model_config = ModelConfig()
            model_config.vocab_size = len(self.tokenizer)
            model_config.use_flash_attention = False
            
            self.model = IntegratedThinkerLLM(model_config)
            self.model.resize_token_embeddings(len(self.tokenizer))
            self.model.to(self.device)
            self.model.eval()
            
            logger.info(f"Model initialized on {self.device}")
            logger.info(f"Parameters: {sum(p.numel() for p in self.model.parameters()):,}")
            
        except Exception as e:
            logger.error(f"Failed to initialize model: {e}")
            raise
    
    def simple_generate(self, prompt, max_length=200):
        """Simple generation without thinking process."""
        if not self.model or not self.tokenizer:
            return "Error: Model not loaded"
        
        try:
            # Tokenize input
            inputs = self.tokenizer(prompt, return_tensors="pt", truncation=True, max_length=256)
            input_ids = inputs["input_ids"].to(self.device)
            attention_mask = inputs["attention_mask"].to(self.device)
            
            # Generate
            with torch.no_grad():
                outputs = self.model.generate(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    max_length=input_ids.size(1) + max_length,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.pad_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )
            
            # Decode response
            generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            response = generated_text[len(prompt):].strip()
            
            return response
            
        except Exception as e:
            logger.error(f"Generation failed: {e}")
            return f"Error: {str(e)}"

def create_interface():
    """Create the Gradio interface."""
    
    # Initialize the UI
    ui = SimpleThinkerUI()
    
    with gr.Blocks(title="Simple ThinkerLLM Demo") as interface:
        gr.Markdown("# 🧠 Simple ThinkerLLM Demo")
        gr.Markdown("Basic text generation with ThinkerLLM")
        
        with gr.Row():
            with gr.Column():
                prompt_input = gr.Textbox(
                    label="Prompt",
                    placeholder="Enter your prompt here...",
                    lines=3,
                    value="Hello, how are you?"
                )
                
                max_length = gr.Slider(
                    minimum=50, maximum=500, value=200, step=50,
                    label="Max Length"
                )
                
                generate_btn = gr.Button("Generate", variant="primary")
                clear_btn = gr.Button("Clear")
            
            with gr.Column():
                output = gr.Textbox(
                    label="Generated Response",
                    lines=10,
                    interactive=False
                )
        
        # Event handlers
        generate_btn.click(
            fn=ui.simple_generate,
            inputs=[prompt_input, max_length],
            outputs=[output]
        )
        
        clear_btn.click(
            fn=lambda: ("", ""),
            outputs=[prompt_input, output]
        )
    
    return interface

def main():
    """Main function."""
    print("🧠 Starting Simple ThinkerLLM Web UI...")
    
    try:
        interface = create_interface()
        print("✅ Interface created successfully")
        
        print("🚀 Launching web UI...")
        print("   Access at: http://127.0.0.1:7860")
        
        interface.launch(
            server_name="127.0.0.1",
            server_port=7860,
            share=False,
            show_error=True
        )
        
    except Exception as e:
        print(f"❌ Failed to launch web UI: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
