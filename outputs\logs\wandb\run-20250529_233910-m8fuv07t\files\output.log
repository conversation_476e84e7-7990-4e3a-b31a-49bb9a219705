INFO:__main__:Starting training...
INFO:thinker_llm.training.trainer:Starting training...
INFO:thinker_llm.training.trainer:Total epochs: 1
INFO:thinker_llm.training.trainer:Total steps: 19944
Epoch 1/1:   0%|                                                                                                                             | 0/19944 [00:00<?, ?it/s]d:\vlinhd11\projects\lcms_llm\moe-llm\geneticai\v4\thinker_llm\training\trainer.py:200: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
  with autocast(device_type='cuda', dtype=self.mixed_precision_dtype):
ERROR:__main__:Training failed with error: autocast.__init__() got an unexpected keyword argument 'device_type'                                                        
Traceback (most recent call last):
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\scripts\train_thinker_llm.py", line 280, in <module>
    main()
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\scripts\train_thinker_llm.py", line 244, in main
    trainer.train()
  File "d:\vlinhd11\projects\lcms_llm\moe-llm\geneticai\v4\thinker_llm\training\trainer.py", line 442, in train
    epoch_metrics = self.train_epoch()
                    ^^^^^^^^^^^^^^^^^^
  File "d:\vlinhd11\projects\lcms_llm\moe-llm\geneticai\v4\thinker_llm\training\trainer.py", line 200, in train_epoch
    with autocast(device_type='cuda', dtype=self.mixed_precision_dtype):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\typing_extensions.py", line 2853, in wrapper
    return arg(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^
TypeError: autocast.__init__() got an unexpected keyword argument 'device_type'
