INFO:__main__:Starting training...
INFO:thinker_llm.training.trainer:Starting training...
INFO:thinker_llm.training.trainer:Total epochs: 2
INFO:thinker_llm.training.trainer:Total steps: 9972
Epoch 1/2:   2%|█▌                                                                           | 98/4986 [00:07<05:38, 14.42it/s, loss=2.7403, lr=5.00e-06]INFO:thinker_llm.training.trainer:train/loss: 2.7403
INFO:thinker_llm.training.trainer:train/learning_rate: 0.0000
INFO:thinker_llm.training.trainer:train/global_step: 25.0000
Epoch 1/2:   4%|███                                                                         | 198/4986 [00:14<05:33, 14.37it/s, loss=2.7412, lr=1.00e-05]INFO:thinker_llm.training.trainer:train/loss: 2.7412
INFO:thinker_llm.training.trainer:train/learning_rate: 0.0000
INFO:thinker_llm.training.trainer:train/global_step: 50.0000
Epoch 1/2:   6%|████▌                                                                       | 298/4986 [00:21<05:24, 14.42it/s, loss=2.7342, lr=1.50e-05]INFO:thinker_llm.training.trainer:train/loss: 2.7342
INFO:thinker_llm.training.trainer:train/learning_rate: 0.0000
INFO:thinker_llm.training.trainer:train/global_step: 75.0000
Epoch 1/2:   8%|██████                                                                      | 398/4986 [00:28<05:19, 14.38it/s, loss=2.7303, lr=2.00e-05]INFO:thinker_llm.training.trainer:train/loss: 2.7303
INFO:thinker_llm.training.trainer:train/learning_rate: 0.0000
INFO:thinker_llm.training.trainer:train/global_step: 100.0000
                                                                                                                                                         INFO:thinker_llm.training.trainer:Checkpoint saved: ./outputs/improved_training\best_checkpoint.pt
INFO:thinker_llm.training.trainer:val/loss: 6.9038                                                                                                       
INFO:thinker_llm.training.trainer:val/llm_lm_loss: 6.7402
INFO:thinker_llm.training.trainer:val/llm_total_loss: 6.7402
INFO:thinker_llm.training.trainer:val/decision_decision_loss: 0.2500
INFO:thinker_llm.training.trainer:val/decision_calibration_loss: 0.0156
INFO:thinker_llm.training.trainer:val/decision_efficiency_loss: 0.0900
INFO:thinker_llm.training.trainer:val/decision_total_loss: 0.2727
INFO:thinker_llm.training.trainer:val/total_loss: 6.9038
Epoch 1/2:  10%|███████▌                                                                    | 498/4986 [00:36<05:22, 13.90it/s, loss=2.7257, lr=2.00e-05]INFO:thinker_llm.training.trainer:train/loss: 2.7257
INFO:thinker_llm.training.trainer:train/learning_rate: 0.0000
INFO:thinker_llm.training.trainer:train/global_step: 125.0000
Traceback (most recent call last):                                                                                                                       
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\train_improved_thinker.py", line 321, in <module>
    main()
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\train_improved_thinker.py", line 315, in main
    trainer.train()
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\thinker_llm\training\trainer.py", line 442, in train
    epoch_metrics = self.train_epoch()
                    ^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\thinker_llm\training\trainer.py", line 218, in train_epoch
    loss_dict = self.loss_fn(
                ^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\thinker_llm\training\losses.py", line 487, in forward
    thinker_losses = self.thinker_loss(
                     ^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\thinker_llm\training\losses.py", line 117, in forward
    thought_loss = self.thought_loss_fn(flat_logits, flat_labels)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\loss.py", line 1297, in forward
    return F.cross_entropy(
           ^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\functional.py", line 3494, in cross_entropy
    return torch._C._nn.cross_entropy_loss(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ValueError: Expected input batch_size (255) to match target batch_size (1020).
