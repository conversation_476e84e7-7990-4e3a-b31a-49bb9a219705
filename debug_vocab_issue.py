#!/usr/bin/env python3
"""
Debug script to identify vocabulary size and token ID issues.
"""

import sys
import os
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent))

import torch
import logging
from transformers import AutoTokenizer
import yaml

# Import ThinkerLLM components
from thinker_llm.models import IntegratedThinkerLLM
from thinker_llm.utils.config import ModelConfig, TrainingConfig
from thinker_llm.training.data_loader import ThinkerDataset

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def debug_tokenizer_and_vocab():
    """Debug tokenizer and vocabulary size issues."""
    logger.info("=== Debugging Tokenizer and Vocabulary ===")
    
    # Load tokenizer
    tokenizer_name = "gpt2"
    tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    logger.info(f"Original tokenizer vocab size: {len(tokenizer)}")
    
    # Add special tokens
    special_tokens = ["<|thinking|>", "<|/thinking|>", "<|response|>", "<|/response|>"]
    new_tokens = []
    for token in special_tokens:
        if token not in tokenizer.get_vocab():
            new_tokens.append(token)
    
    if new_tokens:
        num_added = tokenizer.add_tokens(new_tokens)
        logger.info(f"Added {num_added} special tokens: {new_tokens}")
    
    final_vocab_size = len(tokenizer)
    logger.info(f"Final tokenizer vocab size: {final_vocab_size}")
    
    # Test token IDs
    for token in special_tokens:
        token_id = tokenizer.convert_tokens_to_ids(token)
        logger.info(f"Token '{token}' -> ID {token_id}")
        if token_id >= final_vocab_size:
            logger.error(f"ERROR: Token ID {token_id} is out of bounds for vocab size {final_vocab_size}")
    
    return tokenizer, final_vocab_size


def debug_model_vocab_size(tokenizer, vocab_size):
    """Debug model vocabulary size."""
    logger.info("=== Debugging Model Vocabulary ===")
    
    # Create model config
    model_config = ModelConfig()
    logger.info(f"Original model config vocab size: {model_config.vocab_size}")
    
    # Update vocab size
    model_config.vocab_size = vocab_size
    logger.info(f"Updated model config vocab size: {model_config.vocab_size}")
    
    # Create model
    model = IntegratedThinkerLLM(model_config)
    
    # Check model component vocab sizes
    logger.info(f"Decision mechanism vocab size: {model.decision_mechanism.config.vocab_size}")
    logger.info(f"Thinker module vocab size: {model.thinker_module.config.vocab_size}")
    logger.info(f"Main LLM vocab size: {model.main_llm.config.vocab_size}")
    
    # Check embedding layers
    logger.info(f"Thinker token embeddings: {model.thinker_module.token_embeddings.num_embeddings}")
    logger.info(f"Thinker thought head out features: {model.thinker_module.thought_head.out_features}")
    logger.info(f"Main LLM token embeddings: {model.main_llm.token_embeddings.num_embeddings}")
    logger.info(f"Main LLM lm head out features: {model.main_llm.lm_head.out_features}")
    
    return model


def debug_data_loading(tokenizer, data_path="data/train_cleaned.jsonl"):
    """Debug data loading and token IDs."""
    logger.info("=== Debugging Data Loading ===")
    
    if not Path(data_path).exists():
        logger.warning(f"Data file {data_path} not found. Skipping data loading debug.")
        return
    
    try:
        # Create dataset
        dataset = ThinkerDataset(
            data_path=data_path,
            tokenizer=tokenizer,
            max_length=512,  # Smaller for testing
            thinking_ratio=0.3,
            split="train",
        )
        
        logger.info(f"Dataset created with {len(dataset)} samples")
        
        # Test first few samples
        for i in range(min(3, len(dataset))):
            logger.info(f"\n--- Sample {i} ---")
            sample = dataset[i]
            
            for key, value in sample.items():
                if isinstance(value, torch.Tensor):
                    logger.info(f"{key}: shape={value.shape}, dtype={value.dtype}")
                    if key in ["input_ids", "labels", "thinking_ids", "thinking_labels"]:
                        # Check for out-of-bounds token IDs
                        valid_tokens = value[value != -100]  # Exclude padding/ignore tokens
                        if len(valid_tokens) > 0:
                            min_token = valid_tokens.min().item()
                            max_token = valid_tokens.max().item()
                            logger.info(f"  {key} token range: [{min_token}, {max_token}]")
                            
                            if max_token >= len(tokenizer):
                                logger.error(f"  ERROR: Max token {max_token} >= vocab size {len(tokenizer)}")
                            if min_token < 0 and min_token != -100:
                                logger.error(f"  ERROR: Invalid token {min_token}")
                else:
                    logger.info(f"{key}: {value}")
                    
    except Exception as e:
        logger.error(f"Error in data loading: {e}")
        import traceback
        traceback.print_exc()


def debug_forward_pass(model, tokenizer):
    """Debug a simple forward pass."""
    logger.info("=== Debugging Forward Pass ===")
    
    try:
        # Create a simple input
        text = "What is the capital of France?"
        inputs = tokenizer(text, return_tensors="pt", max_length=64, padding="max_length", truncation=True)
        
        logger.info(f"Input shape: {inputs['input_ids'].shape}")
        logger.info(f"Input token range: [{inputs['input_ids'].min().item()}, {inputs['input_ids'].max().item()}]")
        
        # Move to device if CUDA available
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model.to(device)
        inputs = {k: v.to(device) for k, v in inputs.items()}
        
        logger.info(f"Using device: {device}")
        
        # Forward pass
        with torch.no_grad():
            outputs = model(
                input_ids=inputs["input_ids"],
                attention_mask=inputs["attention_mask"],
                return_thoughts=True,
            )
        
        logger.info("Forward pass successful!")
        logger.info(f"Output logits shape: {outputs['logits'].shape}")
        logger.info(f"Logits vocab dimension: {outputs['logits'].size(-1)}")
        
    except Exception as e:
        logger.error(f"Error in forward pass: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Main debug function."""
    logger.info("Starting vocabulary and model debugging...")
    
    # Debug tokenizer
    tokenizer, vocab_size = debug_tokenizer_and_vocab()
    
    # Debug model
    model = debug_model_vocab_size(tokenizer, vocab_size)
    
    # Debug data loading
    debug_data_loading(tokenizer)
    
    # Debug forward pass
    debug_forward_pass(model, tokenizer)
    
    logger.info("Debugging completed!")


if __name__ == "__main__":
    main()
