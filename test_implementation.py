"""
Test script to verify ThinkerLLM implementation.

This script performs basic tests to ensure all components work correctly.
"""

import torch
from transformers import AutoTokenizer
import logging
import sys
import traceback

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_imports():
    """Test that all modules can be imported."""
    logger.info("Testing imports...")

    try:
        from thinker_llm.models import (
            ThinkerModule, MainLLM, DecisionMechanism, IntegratedThinkerLLM
        )
        from thinker_llm.utils.config import ModelConfig, TrainingConfig
        from thinker_llm.training.data_loader import create_synthetic_data
        from thinker_llm.inference.pipeline import ThinkerLLMPipeline
        from thinker_llm.inference.config import InferenceConfig

        logger.info("✓ All imports successful")
        return True
    except Exception as e:
        logger.error(f"✗ Import failed: {e}")
        traceback.print_exc()
        return False


def test_model_creation():
    """Test model creation and basic functionality."""
    logger.info("Testing model creation...")

    try:
        from thinker_llm.models import IntegratedThinkerLL<PERSON>
        from thinker_llm.utils.config import ModelConfig

        # Create small model for testing
        config = ModelConfig(
            vocab_size=1000,  # Small vocab for testing
            hidden_size=128,
            num_layers=2,
            num_attention_heads=4,
            thinker_hidden_size=256,
            thinker_num_layers=2,
            max_position_embeddings=512,
            use_flash_attention=False,  # Disable for testing
        )

        model = IntegratedThinkerLLM(config)

        # Test model info
        model_info = model.get_model_info()
        logger.info(f"Model created with {model_info['total_parameters']:,} parameters")

        # Test forward pass
        batch_size = 2
        seq_len = 10
        input_ids = torch.randint(0, config.vocab_size, (batch_size, seq_len))
        attention_mask = torch.ones_like(input_ids)

        with torch.no_grad():
            outputs = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                return_thoughts=True,
            )

        # Check outputs
        assert "logits" in outputs
        assert "decision_info" in outputs
        assert outputs["logits"].shape == (batch_size, seq_len, config.vocab_size)

        logger.info("✓ Model creation and forward pass successful")
        return True, model, config

    except Exception as e:
        logger.error(f"✗ Model creation failed: {e}")
        traceback.print_exc()
        return False, None, None


def test_individual_components():
    """Test individual model components."""
    logger.info("Testing individual components...")

    try:
        from thinker_llm.models import ThinkerModule, MainLLM, DecisionMechanism
        from thinker_llm.utils.config import ModelConfig

        config = ModelConfig(
            vocab_size=1000,
            hidden_size=128,
            num_layers=2,
            num_attention_heads=4,
            thinker_hidden_size=256,
            thinker_num_layers=2,
            use_flash_attention=False,  # Disable for testing
        )

        # Test ThinkerModule
        thinker = ThinkerModule(config)
        input_ids = torch.randint(0, config.vocab_size, (2, 10))

        with torch.no_grad():
            thinker_outputs = thinker(input_ids)

        assert "thought_logits" in thinker_outputs
        assert "reasoning_states" in thinker_outputs

        # Test MainLLM
        main_llm = MainLLM(config)

        with torch.no_grad():
            llm_outputs = main_llm(input_ids)

        assert "logits" in llm_outputs

        # Test DecisionMechanism
        decision = DecisionMechanism(config)

        with torch.no_grad():
            decision_outputs = decision(input_ids)

        assert "thinking_probability" in decision_outputs
        assert "thinking_decision" in decision_outputs

        logger.info("✓ Individual components test successful")
        return True

    except Exception as e:
        logger.error(f"✗ Individual components test failed: {e}")
        traceback.print_exc()
        return False


def test_data_loading():
    """Test data loading functionality."""
    logger.info("Testing data loading...")

    try:
        from thinker_llm.training.data_loader import create_synthetic_data, prepare_dataset
        import os

        # Create test data
        os.makedirs("./test_data", exist_ok=True)
        create_synthetic_data(
            num_samples=10,
            output_path="./test_data/test.jsonl",
            thinking_ratio=0.5,
        )

        # Test data loading
        dataset, dataloader = prepare_dataset(
            data_path="./test_data/test.jsonl",
            tokenizer_name="gpt2",
            max_length=128,
            batch_size=2,
        )

        # Test getting a batch
        data_iter = iter(dataloader.get_dataloader())
        batch = next(data_iter)

        assert "input_ids" in batch
        assert "attention_mask" in batch
        assert "labels" in batch

        logger.info(f"✓ Data loading successful. Dataset size: {len(dataset)}")
        return True

    except Exception as e:
        logger.error(f"✗ Data loading test failed: {e}")
        traceback.print_exc()
        return False


def test_inference_pipeline():
    """Test inference pipeline."""
    logger.info("Testing inference pipeline...")

    try:
        from thinker_llm.models import IntegratedThinkerLLM
        from thinker_llm.utils.config import ModelConfig
        from thinker_llm.inference.pipeline import ThinkerLLMPipeline
        from thinker_llm.inference.config import InferenceConfig
        from transformers import AutoTokenizer

        # Create small model
        config = ModelConfig(
            vocab_size=50257,  # GPT-2 vocab size
            hidden_size=128,
            num_layers=2,
            num_attention_heads=4,
            thinker_hidden_size=256,
            thinker_num_layers=2,
            use_flash_attention=False,  # Disable for testing
        )

        model = IntegratedThinkerLLM(config)
        tokenizer = AutoTokenizer.from_pretrained("gpt2")
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token

        # Create pipeline
        inference_config = InferenceConfig(
            max_new_tokens=20,  # Small for testing
            temperature=0.7,
            show_thinking=True,
        )

        pipeline = ThinkerLLMPipeline(
            model=model,
            tokenizer=tokenizer,
            config=inference_config,
        )

        # Test generation
        result = pipeline.generate("What is 2+2?")

        assert "response" in result
        assert "used_thinking" in result
        assert "thinking_probability" in result

        logger.info("✓ Inference pipeline test successful")
        return True

    except Exception as e:
        logger.error(f"✗ Inference pipeline test failed: {e}")
        traceback.print_exc()
        return False


def test_training_setup():
    """Test training setup (without actual training)."""
    logger.info("Testing training setup...")

    try:
        from thinker_llm.models import IntegratedThinkerLLM
        from thinker_llm.utils.config import ModelConfig, TrainingConfig
        from thinker_llm.training.trainer import ThinkerLLMTrainer
        from thinker_llm.training.data_loader import prepare_dataset
        from thinker_llm.training.losses import CombinedLoss

        # Create configurations
        model_config = ModelConfig(
            vocab_size=1000,
            hidden_size=128,
            num_layers=2,
            num_attention_heads=4,
            thinker_hidden_size=256,
            thinker_num_layers=2,
            use_flash_attention=False,  # Disable for testing
        )

        training_config = TrainingConfig(
            batch_size=2,
            learning_rate=1e-4,
            num_epochs=1,
            output_dir="./test_outputs",
        )

        # Create model
        model = IntegratedThinkerLLM(model_config)

        # Test loss function
        loss_fn = CombinedLoss(vocab_size=model_config.vocab_size)

        # Create dummy data
        batch_size = 2
        seq_len = 10
        input_ids = torch.randint(0, model_config.vocab_size, (batch_size, seq_len))
        labels = torch.randint(0, model_config.vocab_size, (batch_size, seq_len))

        # Test forward pass and loss computation
        with torch.no_grad():
            outputs = model(input_ids=input_ids, labels=labels, return_thoughts=True)
            losses = loss_fn(
                model_outputs=outputs,
                labels=labels,
                decision_labels=torch.ones(batch_size),
            )

        assert "total_loss" in losses
        assert losses["total_loss"].item() > 0

        logger.info("✓ Training setup test successful")
        return True

    except Exception as e:
        logger.error(f"✗ Training setup test failed: {e}")
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    logger.info("Starting ThinkerLLM implementation tests...")

    tests = [
        ("Imports", test_imports),
        ("Model Creation", test_model_creation),
        ("Individual Components", test_individual_components),
        ("Data Loading", test_data_loading),
        ("Inference Pipeline", test_inference_pipeline),
        ("Training Setup", test_training_setup),
    ]

    results = []

    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running test: {test_name}")
        logger.info('='*50)

        try:
            if test_name == "Model Creation":
                success, model, config = test_func()
                results.append((test_name, success))
            else:
                success = test_func()
                results.append((test_name, success))
        except Exception as e:
            logger.error(f"Test {test_name} failed with exception: {e}")
            results.append((test_name, False))

    # Print summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info('='*50)

    passed = 0
    total = len(results)

    for test_name, success in results:
        status = "PASS" if success else "FAIL"
        logger.info(f"{test_name}: {status}")
        if success:
            passed += 1

    logger.info(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        logger.info("🎉 All tests passed! ThinkerLLM implementation is working correctly.")
        return 0
    else:
        logger.error(f"❌ {total - passed} tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
