"""
Debug training issues with minimal example.
"""

import torch
import os
import logging

# Force CPU usage
os.environ["CUDA_VISIBLE_DEVICES"] = ""

from transformers import AutoTokenizer
from thinker_llm.models import IntegratedThinkerLLM
from thinker_llm.utils.config import ModelConfig
from thinker_llm.training.losses import CombinedLoss

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def debug_model_forward():
    """Debug model forward pass."""
    logger.info("Testing model forward pass...")
    
    # Very small model
    config = ModelConfig(
        vocab_size=1000,  # Small vocab
        hidden_size=64,
        num_layers=2,
        num_attention_heads=2,
        thinker_hidden_size=96,
        thinker_num_layers=2,
        use_flash_attention=False,
        gradient_checkpointing=False,
    )
    
    model = IntegratedThinkerLLM(config)
    logger.info("Model created")
    
    # Create simple input
    batch_size = 1
    seq_len = 5
    input_ids = torch.randint(0, config.vocab_size, (batch_size, seq_len))
    attention_mask = torch.ones_like(input_ids)
    labels = torch.randint(0, config.vocab_size, (batch_size, seq_len))
    
    logger.info(f"Input shape: {input_ids.shape}")
    logger.info(f"Input range: {input_ids.min().item()} to {input_ids.max().item()}")
    logger.info(f"Labels range: {labels.min().item()} to {labels.max().item()}")
    
    # Forward pass
    try:
        with torch.no_grad():
            outputs = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                labels=labels,
                return_thoughts=True,
            )
        logger.info("✅ Forward pass successful")
        logger.info(f"Logits shape: {outputs['logits'].shape}")
        return True, outputs, labels
    except Exception as e:
        logger.error(f"❌ Forward pass failed: {e}")
        return False, None, None


def debug_loss_computation():
    """Debug loss computation."""
    logger.info("Testing loss computation...")
    
    success, outputs, labels = debug_model_forward()
    if not success:
        return False
    
    # Create loss function
    loss_fn = CombinedLoss(vocab_size=1000)
    
    try:
        # Test with minimal inputs
        decision_labels = torch.tensor([1.0])  # Single sample
        
        logger.info("Computing loss...")
        losses = loss_fn(
            model_outputs=outputs,
            labels=labels,
            decision_labels=decision_labels,
        )
        
        logger.info("✅ Loss computation successful")
        logger.info(f"Total loss: {losses['total_loss'].item()}")
        return True
    except Exception as e:
        logger.error(f"❌ Loss computation failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def debug_data_preparation():
    """Debug data preparation."""
    logger.info("Testing data preparation...")
    
    from thinker_llm.training.data_loader import ThinkerDataset, ThinkerDataLoader
    import json
    import tempfile
    
    # Create minimal sample data
    samples = [
        {
            "instruction": "What is 2+2?",
            "thinking": "",
            "output": "4",
            "requires_thinking": False,
        }
    ]
    
    # Save to temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.jsonl', delete=False) as f:
        for sample in samples:
            f.write(json.dumps(sample) + '\n')
        temp_path = f.name
    
    try:
        # Load tokenizer
        tokenizer = AutoTokenizer.from_pretrained("gpt2")
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        # Create dataset
        dataset = ThinkerDataset(
            data_path=temp_path,
            tokenizer=tokenizer,
            max_length=64,  # Very short
            thinking_ratio=0.0,  # No thinking for simplicity
            split="train",
        )
        
        logger.info(f"Dataset created with {len(dataset)} samples")
        
        # Get a sample
        sample = dataset[0]
        logger.info(f"Sample keys: {sample.keys()}")
        logger.info(f"Input IDs shape: {sample['input_ids'].shape}")
        logger.info(f"Input IDs range: {sample['input_ids'].min().item()} to {sample['input_ids'].max().item()}")
        logger.info(f"Labels range: {sample['labels'].min().item()} to {sample['labels'].max().item()}")
        
        # Check for invalid values
        invalid_labels = (sample['labels'] < -100) | (sample['labels'] >= len(tokenizer))
        if invalid_labels.any():
            logger.warning(f"Found invalid labels: {sample['labels'][invalid_labels]}")
        
        logger.info("✅ Data preparation successful")
        return True, sample
    except Exception as e:
        logger.error(f"❌ Data preparation failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None
    finally:
        # Clean up
        os.unlink(temp_path)


def debug_simple_training_step():
    """Debug a simple training step."""
    logger.info("Testing simple training step...")
    
    # Get data sample
    success, sample = debug_data_preparation()
    if not success:
        return False
    
    # Create model
    config = ModelConfig(
        vocab_size=50257,  # GPT-2 vocab size
        hidden_size=64,
        num_layers=2,
        num_attention_heads=2,
        thinker_hidden_size=96,
        thinker_num_layers=2,
        use_flash_attention=False,
        gradient_checkpointing=False,
    )
    
    model = IntegratedThinkerLLM(config)
    loss_fn = CombinedLoss(vocab_size=config.vocab_size)
    
    try:
        # Prepare inputs
        input_ids = sample['input_ids'].unsqueeze(0)  # Add batch dimension
        attention_mask = sample['attention_mask'].unsqueeze(0)
        labels = sample['labels'].unsqueeze(0)
        decision_labels = torch.tensor([sample['requires_thinking']], dtype=torch.float)
        
        logger.info(f"Batch input shape: {input_ids.shape}")
        logger.info(f"Batch labels shape: {labels.shape}")
        logger.info(f"Decision labels: {decision_labels}")
        
        # Forward pass
        outputs = model(
            input_ids=input_ids,
            attention_mask=attention_mask,
            labels=labels,
            return_thoughts=True,
        )
        
        logger.info("Forward pass completed")
        
        # Loss computation
        losses = loss_fn(
            model_outputs=outputs,
            labels=labels,
            decision_labels=decision_labels,
        )
        
        logger.info(f"✅ Training step successful")
        logger.info(f"Total loss: {losses['total_loss'].item()}")
        
        # Test backward pass
        losses['total_loss'].backward()
        logger.info("✅ Backward pass successful")
        
        return True
    except Exception as e:
        logger.error(f"❌ Training step failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all debug tests."""
    logger.info("Starting debug tests...")
    
    tests = [
        ("Model Forward", debug_model_forward),
        ("Loss Computation", debug_loss_computation),
        ("Data Preparation", debug_data_preparation),
        ("Simple Training Step", debug_simple_training_step),
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info('='*50)
        
        try:
            if test_name == "Model Forward":
                success, _, _ = test_func()
            elif test_name == "Data Preparation":
                success, _ = test_func()
            else:
                success = test_func()
            
            if success:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
                break
        except Exception as e:
            logger.error(f"❌ {test_name}: CRASHED - {e}")
            import traceback
            traceback.print_exc()
            break
    
    logger.info("Debug tests completed")


if __name__ == "__main__":
    main()
