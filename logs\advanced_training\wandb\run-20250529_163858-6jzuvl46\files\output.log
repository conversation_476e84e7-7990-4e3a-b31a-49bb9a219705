INFO:__main__:Model size: 260,893,962 parameters (995.2 MB)
INFO:thinker_llm.training.trainer:Starting training...
INFO:thinker_llm.training.trainer:Total epochs: 3
INFO:thinker_llm.training.trainer:Total steps: 180
Traceback (most recent call last):                                                                                                                          
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\examples\training_example.py", line 320, in <module>
    main()
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\examples\training_example.py", line 311, in main
    train_with_different_strategies()
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\examples\training_example.py", line 169, in train_with_different_strategies
    trainer.train()
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\thinker_llm\training\trainer.py", line 275, in train
    epoch_metrics = self.train_epoch()
                    ^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\thinker_llm\training\trainer.py", line 129, in train_epoch
    outputs = self.model(
              ^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\thinker_llm\models\integrated_model.py", line 104, in forward
    should_think_mask, decision_info = self.decision_mechanism.should_think(
                                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\thinker_llm\models\decision_mechanism.py", line 304, in should_think
    decision_info = self.forward(input_ids, attention_mask)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\thinker_llm\models\decision_mechanism.py", line 235, in forward
    text_features = self.text_feature_extractor(input_ids, attention_mask)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\thinker_llm\models\decision_mechanism.py", line 37, in forward
    embeds = embeds * mask
             ~~~~~~~^~~~~~
RuntimeError: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.
