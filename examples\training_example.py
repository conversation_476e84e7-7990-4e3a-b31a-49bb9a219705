"""
Advanced training example for ThinkerLLM.

This script demonstrates:
1. Different training strategies (joint, separate, frozen components)
2. Custom data preparation
3. Advanced training configurations
4. Model evaluation and analysis
"""
import sys
import os
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent.parent))
import torch
from transformers import AutoTokenizer
import logging


from thinker_llm.models import IntegratedThinkerLLM
from thinker_llm.utils.config import ModelConfig, TrainingConfig
from thinker_llm.training.data_loader import ThinkerDataset, ThinkerDataLoader
from thinker_llm.training.trainer import ThinkerLLMTrainer
from thinker_llm.utils.model_utils import save_model, get_model_size_info

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_advanced_training_data():
    """Create more sophisticated training data."""
    import json
    
    # More diverse and realistic training examples
    training_samples = [
        # Simple factual questions (no thinking needed)
        {
            "instruction": "What is the capital of Japan?",
            "thinking": "",
            "output": "The capital of Japan is Tokyo.",
            "requires_thinking": False,
        },
        {
            "instruction": "What color is the sky?",
            "thinking": "",
            "output": "The sky is typically blue during the day.",
            "requires_thinking": False,
        },
        
        # Mathematical problems (thinking needed)
        {
            "instruction": "If I have 15 apples and give away 7, then buy 12 more, how many apples do I have?",
            "thinking": "Let me work through this step by step.\n\nStarting amount: 15 apples\nGive away: 7 apples\nAfter giving away: 15 - 7 = 8 apples\nBuy more: 12 apples\nFinal amount: 8 + 12 = 20 apples",
            "output": "You would have 20 apples. Starting with 15, after giving away 7 you'd have 8, then adding 12 more gives you 20 total.",
            "requires_thinking": True,
        },
        {
            "instruction": "A rectangle has a length of 8 meters and width of 5 meters. What is its area and perimeter?",
            "thinking": "I need to calculate both area and perimeter for a rectangle.\n\nFor area: Area = length × width\nArea = 8 × 5 = 40 square meters\n\nFor perimeter: Perimeter = 2 × (length + width)\nPerimeter = 2 × (8 + 5) = 2 × 13 = 26 meters",
            "output": "The rectangle has an area of 40 square meters and a perimeter of 26 meters.",
            "requires_thinking": True,
        },
        
        # Reasoning and analysis (thinking needed)
        {
            "instruction": "Why might someone choose to walk instead of drive to work?",
            "thinking": "This question asks me to consider multiple factors that might influence transportation choices. Let me think through various reasons:\n\n1. Health benefits - walking provides exercise\n2. Environmental concerns - walking produces no emissions\n3. Cost savings - no fuel, parking, or vehicle maintenance costs\n4. Convenience factors - might be faster in heavy traffic, no parking needed\n5. Personal preference - some people enjoy walking\n6. Distance - only feasible for shorter distances",
            "output": "There are several reasons someone might choose to walk to work: health benefits from exercise, environmental consciousness to reduce emissions, cost savings on fuel and parking, convenience in areas with heavy traffic or limited parking, personal enjoyment of walking, and practicality if the distance is reasonable.",
            "requires_thinking": True,
        },
        
        # Creative tasks (thinking needed)
        {
            "instruction": "Write a short story about a robot learning to paint.",
            "thinking": "I need to create a short story with these elements:\n- A robot character\n- Learning process\n- Painting as the activity\n- Some kind of narrative arc\n\nI'll focus on the robot's journey from mechanical precision to artistic expression.",
            "output": "ARTIE-7 had been programmed for precision—every circuit calculated, every movement exact. But when the art teacher handed it a paintbrush, something unexpected happened. Its first attempts were perfect geometric shapes, flawless but lifeless. Day by day, ARTIE watched human students splash colors with wild abandon. Slowly, it began to understand that art wasn't about perfection—it was about expression. The day ARTIE painted its first sunset, with colors bleeding beautifully into each other, it finally understood what it meant to create rather than simply execute.",
            "requires_thinking": True,
        },
    ]
    
    # Create train/validation split
    train_data = training_samples * 20  # Repeat for more data
    val_data = training_samples[:3]  # Small validation set
    
    # Save training data
    os.makedirs("./data", exist_ok=True)
    
    with open("./data/advanced_train.jsonl", "w") as f:
        for sample in train_data:
            f.write(json.dumps(sample) + "\n")
    
    with open("./data/advanced_val.jsonl", "w") as f:
        for sample in val_data:
            f.write(json.dumps(sample) + "\n")
    
    logger.info(f"Created {len(train_data)} training samples and {len(val_data)} validation samples")


def train_with_different_strategies():
    """Demonstrate different training strategies."""
    
    # Configuration
    model_config = ModelConfig(
        vocab_size=50257,
        hidden_size=768,
        num_layers=8,
        num_attention_heads=12,
        thinker_hidden_size=1024,
        thinker_num_layers=6,
        max_position_embeddings=1024,
    )
    
    training_config = TrainingConfig(
        batch_size=2,
        learning_rate=3e-5,
        num_epochs=3,
        warmup_steps=50,
        max_length=512,
        thinking_ratio=0.4,
        output_dir="./outputs/advanced_training",
        logging_dir="./logs/advanced_training",
        save_steps=50,
        eval_steps=25,
    )
    
    # Initialize tokenizer
    tokenizer = AutoTokenizer.from_pretrained("gpt2")
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Prepare datasets
    train_dataset = ThinkerDataset(
        data_path="./data/advanced_train.jsonl",
        tokenizer=tokenizer,
        max_length=training_config.max_length,
        thinking_ratio=training_config.thinking_ratio,
        split="train",
    )
    
    val_dataset = ThinkerDataset(
        data_path="./data/advanced_val.jsonl",
        tokenizer=tokenizer,
        max_length=training_config.max_length,
        thinking_ratio=training_config.thinking_ratio,
        split="train",  # Using train format for validation
    )
    
    train_dataloader = ThinkerDataLoader(train_dataset, batch_size=training_config.batch_size)
    val_dataloader = ThinkerDataLoader(val_dataset, batch_size=training_config.batch_size, shuffle=False)
    
    # Strategy 1: Joint Training
    logger.info("Strategy 1: Joint Training (all components together)")
    
    model = IntegratedThinkerLLM(model_config)
    model.set_training_mode("joint")
    
    trainer = ThinkerLLMTrainer(
        model=model,
        train_dataloader=train_dataloader.get_dataloader(),
        val_dataloader=val_dataloader.get_dataloader(),
        config=training_config,
        model_config=model_config,
    )
    
    # Print model info
    model_info = get_model_size_info(model)
    logger.info(f"Model size: {model_info['total_parameters']:,} parameters ({model_info['model_size_mb']:.1f} MB)")
    
    # Train
    trainer.train()
    
    # Save model
    save_model(
        model=model,
        tokenizer=tokenizer,
        save_path="./models/joint_trained",
        model_config=model_config,
        training_config=training_config,
        additional_info={"training_strategy": "joint"},
    )
    
    # Strategy 2: Frozen LLM Training
    logger.info("\nStrategy 2: Frozen LLM Training (train only thinker and decision)")
    
    model2 = IntegratedThinkerLLM(model_config)
    model2.set_training_mode("frozen_llm")
    
    # Update config for this strategy
    training_config2 = TrainingConfig(
        **training_config.__dict__,
        output_dir="./outputs/frozen_llm_training",
        learning_rate=5e-5,  # Higher LR since we're training fewer parameters
    )
    
    trainer2 = ThinkerLLMTrainer(
        model=model2,
        train_dataloader=train_dataloader.get_dataloader(),
        val_dataloader=val_dataloader.get_dataloader(),
        config=training_config2,
        model_config=model_config,
    )
    
    trainer2.train()
    
    save_model(
        model=model2,
        tokenizer=tokenizer,
        save_path="./models/frozen_llm_trained",
        model_config=model_config,
        training_config=training_config2,
        additional_info={"training_strategy": "frozen_llm"},
    )
    
    logger.info("Training completed for all strategies!")


def evaluate_models():
    """Evaluate and compare different trained models."""
    from thinker_llm.inference.pipeline import ThinkerLLMPipeline
    from thinker_llm.inference.config import InferenceConfig
    from thinker_llm.utils.model_utils import load_model
    
    logger.info("Evaluating trained models...")
    
    # Test prompts
    test_prompts = [
        "What is 15 + 27?",
        "Explain why exercise is important for health.",
        "If I save $50 per month, how much will I have after 2 years?",
        "What are the benefits of reading books?",
    ]
    
    model_paths = [
        "./models/joint_trained",
        "./models/frozen_llm_trained",
    ]
    
    inference_config = InferenceConfig(
        max_new_tokens=150,
        temperature=0.7,
        show_thinking=True,
    )
    
    results = {}
    
    for model_path in model_paths:
        if not Path(model_path).exists():
            logger.warning(f"Model not found: {model_path}")
            continue
        
        logger.info(f"Evaluating model: {model_path}")
        
        # Load model
        loaded = load_model(model_path)
        model = loaded["model"]
        tokenizer = loaded["tokenizer"]
        
        # Create pipeline
        pipeline = ThinkerLLMPipeline(
            model=model,
            tokenizer=tokenizer,
            config=inference_config,
        )
        
        model_results = []
        
        for prompt in test_prompts:
            result = pipeline.generate(
                text=prompt,
                return_thoughts=True,
            )
            
            model_results.append({
                "prompt": prompt,
                "response": result["response"],
                "used_thinking": result["used_thinking"],
                "thinking_probability": result["thinking_probability"],
                "generation_time": result["generation_time"],
            })
        
        results[model_path] = model_results
    
    # Print comparison
    print("\n" + "="*80)
    print("MODEL EVALUATION RESULTS")
    print("="*80)
    
    for prompt in test_prompts:
        print(f"\nPrompt: {prompt}")
        print("-" * 60)
        
        for model_path, model_results in results.items():
            model_name = Path(model_path).name
            result = next(r for r in model_results if r["prompt"] == prompt)
            
            print(f"\n{model_name}:")
            print(f"  Used Thinking: {result['used_thinking']}")
            print(f"  Thinking Prob: {result['thinking_probability']:.3f}")
            print(f"  Response: {result['response'][:100]}...")
            print(f"  Time: {result['generation_time']:.2f}s")


def main():
    """Main function for advanced training example."""
    
    logger.info("Starting advanced training example...")
    
    # Create training data
    create_advanced_training_data()
    
    # Train with different strategies
    train_with_different_strategies()
    
    # Evaluate models
    evaluate_models()
    
    logger.info("Advanced training example completed!")


if __name__ == "__main__":
    main()
