_wandb:
    value:
        cli_version: 0.19.8
        m: []
        python_version: 3.12.9
        t:
            "1":
                - 1
                - 5
                - 11
                - 49
                - 53
                - 55
                - 71
                - 105
            "2":
                - 1
                - 5
                - 11
                - 49
                - 53
                - 55
                - 71
                - 105
            "3":
                - 16
                - 23
                - 55
            "4": 3.12.9
            "5": 0.19.8
            "6": 4.51.3
            "8":
                - 3
                - 5
            "12": 0.19.8
            "13": windows-amd64
batch_size:
    value: 1
cache_dir:
    value: ./cache
decision_loss_weight:
    value: 0.3
device:
    value: cuda
eval_steps:
    value: 500
freeze_llm:
    value: false
freeze_thinker:
    value: false
gradient_accumulation_steps:
    value: 4
joint_training:
    value: true
learning_rate:
    value: 5e-05
llm_loss_weight:
    value: 1
logging_dir:
    value: ./outputs\logs
logging_steps:
    value: 100
max_grad_norm:
    value: 1
max_length:
    value: 2048
mixed_precision_dtype:
    value: float16
num_epochs:
    value: 1
optimizer:
    value: adamw
output_dir:
    value: ./outputs
pretrain_components:
    value: false
save_steps:
    value: 1000
save_total_limit:
    value: 3
scheduler:
    value: cosine
thinker_loss_weight:
    value: 0.5
thinking_ratio:
    value: 0.3
use_amp:
    value: true
warmup_steps:
    value: 1000
weight_decay:
    value: 0.01
