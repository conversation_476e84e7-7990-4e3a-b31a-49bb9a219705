"""
Inference pipeline for ThinkerLLM.
"""

import torch
from typing import Dict, List, Optional, Union, Any
from transformers import AutoTokenizer
import time

from ..models.integrated_model import IntegratedThinkerLLM
from ..utils.config import ModelConfig, InferenceConfig
from .config import InferenceConfig as InfConfig
from .generation import ThinkerGenerator, StandardGenerator
from .postprocessing import ThoughtProcessor, ResponseFormatter


class ThinkerLLMPipeline:
    """
    Complete inference pipeline for ThinkerLLM that handles:
    1. Input preprocessing
    2. Decision making
    3. Thinking generation (if needed)
    4. Response generation
    5. Output postprocessing
    """
    
    def __init__(
        self,
        model: IntegratedThinkerLLM,
        tokenizer: AutoTokenizer,
        config: InfConfig = None,
        device: str = "auto",
    ):
        self.model = model
        self.tokenizer = tokenizer
        self.config = config or InfConfig()
        
        # Setup device
        if device == "auto":
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            self.device = torch.device(device)
        
        self.model.to(self.device)
        self.model.eval()
        
        # Initialize generators
        self.thinker_generator = ThinkerGenerator(model, tokenizer, config)
        self.standard_generator = StandardGenerator(model, tokenizer, config)
        
        # Initialize processors
        self.thought_processor = ThoughtProcessor(tokenizer)
        self.response_formatter = ResponseFormatter(tokenizer)
        
        # Setup special tokens
        self._setup_special_tokens()
    
    def _setup_special_tokens(self):
        """Setup special tokens for thinking."""
        special_tokens = {
            "thinking_start": "<|thinking|>",
            "thinking_end": "<|/thinking|>",
            "response_start": "<|response|>",
            "response_end": "<|/response|>",
        }
        
        # Add tokens if not present
        new_tokens = []
        for token in special_tokens.values():
            if token not in self.tokenizer.get_vocab():
                new_tokens.append(token)
        
        if new_tokens:
            self.tokenizer.add_tokens(new_tokens)
            # Note: In practice, you'd want to resize model embeddings
            # self.model.resize_token_embeddings(len(self.tokenizer))
        
        # Store token IDs
        self.special_token_ids = {
            name: self.tokenizer.convert_tokens_to_ids(token)
            for name, token in special_tokens.items()
        }
    
    def preprocess_input(self, text: str) -> Dict[str, torch.Tensor]:
        """
        Preprocess input text for the model.
        
        Args:
            text: Input text
            
        Returns:
            Dictionary with tokenized inputs
        """
        # Tokenize input
        encoding = self.tokenizer(
            text,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=self.config.max_new_tokens,
        )
        
        # Move to device
        encoding = {k: v.to(self.device) for k, v in encoding.items()}
        
        return encoding
    
    def generate(
        self,
        text: Union[str, List[str]],
        return_thoughts: bool = None,
        force_thinking: bool = None,
        disable_thinking: bool = None,
        **generation_kwargs
    ) -> Dict[str, Any]:
        """
        Generate response using ThinkerLLM.
        
        Args:
            text: Input text or list of texts
            return_thoughts: Whether to return thinking process
            force_thinking: Force thinking regardless of decision
            disable_thinking: Disable thinking regardless of decision
            **generation_kwargs: Additional generation parameters
            
        Returns:
            Dictionary containing generated outputs
        """
        # Handle defaults
        if return_thoughts is None:
            return_thoughts = self.config.show_thinking
        if force_thinking is None:
            force_thinking = self.config.force_thinking
        if disable_thinking is None:
            disable_thinking = self.config.disable_thinking
        
        # Handle single text input
        if isinstance(text, str):
            text = [text]
        
        # Batch processing
        results = []
        
        for input_text in text:
            start_time = time.time()
            
            # Preprocess input
            inputs = self.preprocess_input(input_text)
            
            # Generate with model
            with torch.no_grad():
                outputs = self.model.generate(
                    input_ids=inputs["input_ids"],
                    attention_mask=inputs["attention_mask"],
                    max_new_tokens=self.config.max_new_tokens,
                    temperature=self.config.temperature,
                    top_p=self.config.top_p,
                    top_k=self.config.top_k,
                    do_sample=self.config.do_sample,
                    num_beams=self.config.num_beams,
                    force_thinking=force_thinking,
                    disable_thinking=disable_thinking,
                    return_thoughts=return_thoughts,
                    thinking_temperature=self.config.thinking_temperature,
                    **generation_kwargs
                )
            
            # Process outputs
            result = self._process_outputs(
                inputs=inputs,
                outputs=outputs,
                input_text=input_text,
                return_thoughts=return_thoughts,
            )
            
            # Add timing information
            result["generation_time"] = time.time() - start_time
            
            results.append(result)
        
        # Return single result if single input
        if len(results) == 1:
            return results[0]
        
        return results
    
    def _process_outputs(
        self,
        inputs: Dict[str, torch.Tensor],
        outputs: Dict[str, torch.Tensor],
        input_text: str,
        return_thoughts: bool,
    ) -> Dict[str, Any]:
        """
        Process model outputs into final result.
        
        Args:
            inputs: Original inputs
            outputs: Model outputs
            input_text: Original input text
            return_thoughts: Whether thoughts were requested
            
        Returns:
            Processed result dictionary
        """
        result = {
            "input": input_text,
            "decision_info": outputs["decision_info"],
            "used_thinking": outputs["should_think_mask"].any().item(),
        }
        
        # Decode generated text
        generated_ids = outputs["generated_ids"]
        input_length = inputs["input_ids"].size(1)
        
        # Extract only the newly generated tokens
        new_tokens = generated_ids[:, input_length:]
        
        # Decode full response
        full_response = self.tokenizer.decode(
            new_tokens[0], 
            skip_special_tokens=False,
            clean_up_tokenization_spaces=True
        )
        
        # Process thoughts if available and requested
        if return_thoughts and "thought_ids" in outputs and outputs["thought_ids"] is not None:
            thought_ids = outputs["thought_ids"]
            if thought_ids.numel() > 0:  # Check if thoughts were generated
                thoughts = self.thought_processor.process_thoughts(thought_ids[0])
                result["thoughts"] = thoughts
                result["raw_thoughts"] = self.tokenizer.decode(
                    thought_ids[0], 
                    skip_special_tokens=False
                )
        
        # Format final response
        formatted_response = self.response_formatter.format_response(
            full_response,
            include_thoughts=return_thoughts and result.get("thoughts"),
            thoughts=result.get("thoughts", "")
        )
        
        result["response"] = formatted_response
        result["raw_response"] = full_response
        
        # Add decision statistics
        decision_info = outputs["decision_info"]
        result["thinking_probability"] = decision_info["thinking_probability"].item()
        result["question_type"] = decision_info.get("question_type")
        
        return result
    
    def chat(
        self,
        messages: List[Dict[str, str]],
        **generation_kwargs
    ) -> Dict[str, Any]:
        """
        Chat interface for conversational use.
        
        Args:
            messages: List of message dictionaries with 'role' and 'content'
            **generation_kwargs: Additional generation parameters
            
        Returns:
            Generated response
        """
        # Format messages into a single prompt
        prompt = self._format_chat_prompt(messages)
        
        # Generate response
        result = self.generate(prompt, **generation_kwargs)
        
        # Add chat-specific formatting
        result["messages"] = messages
        result["role"] = "assistant"
        
        return result
    
    def _format_chat_prompt(self, messages: List[Dict[str, str]]) -> str:
        """
        Format chat messages into a prompt.
        
        Args:
            messages: List of message dictionaries
            
        Returns:
            Formatted prompt string
        """
        prompt_parts = []
        
        for message in messages:
            role = message.get("role", "user")
            content = message.get("content", "")
            
            if role == "system":
                prompt_parts.append(f"System: {content}")
            elif role == "user":
                prompt_parts.append(f"User: {content}")
            elif role == "assistant":
                prompt_parts.append(f"Assistant: {content}")
        
        # Add final assistant prompt
        prompt_parts.append("Assistant:")
        
        return "\n\n".join(prompt_parts)
    
    def batch_generate(
        self,
        texts: List[str],
        batch_size: int = None,
        **generation_kwargs
    ) -> List[Dict[str, Any]]:
        """
        Generate responses for multiple texts in batches.
        
        Args:
            texts: List of input texts
            batch_size: Batch size for processing
            **generation_kwargs: Additional generation parameters
            
        Returns:
            List of generated results
        """
        if batch_size is None:
            batch_size = self.config.batch_size
        
        results = []
        
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            batch_results = self.generate(batch_texts, **generation_kwargs)
            
            if isinstance(batch_results, list):
                results.extend(batch_results)
            else:
                results.append(batch_results)
        
        return results
    
    def analyze_decision(self, text: str) -> Dict[str, Any]:
        """
        Analyze the decision-making process for a given input.
        
        Args:
            text: Input text to analyze
            
        Returns:
            Decision analysis results
        """
        inputs = self.preprocess_input(text)
        
        with torch.no_grad():
            # Get decision mechanism outputs
            should_think_mask, decision_info = self.model.decision_mechanism.should_think(
                input_ids=inputs["input_ids"],
                attention_mask=inputs["attention_mask"],
            )
            
            # Get detailed statistics
            stats = self.model.decision_mechanism.get_decision_statistics(
                input_ids=inputs["input_ids"],
                attention_mask=inputs["attention_mask"],
            )
        
        return {
            "input": text,
            "should_think": should_think_mask.item(),
            "thinking_probability": decision_info["thinking_probability"].item(),
            "question_type": decision_info["question_type"].cpu().numpy().tolist(),
            "statistics": stats,
        }
    
    def update_config(self, **kwargs):
        """Update inference configuration."""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
            else:
                raise ValueError(f"Unknown config parameter: {key}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model."""
        return self.model.get_model_info()
