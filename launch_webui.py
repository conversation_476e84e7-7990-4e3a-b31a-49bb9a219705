#!/usr/bin/env python3
"""
Launch script for ThinkerLLM Web UI
"""

import sys
import os
import subprocess
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed."""
    try:
        import gradio
        print(f"✅ Gradio {gradio.__version__} is installed")
        return True
    except ImportError:
        print("❌ Gradio is not installed")
        return False

def install_dependencies():
    """Install required dependencies."""
    print("Installing web UI dependencies...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements_webui.txt"
        ])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def main():
    """Main launch function."""
    print("🧠 ThinkerLLM Web UI Launcher")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not Path("web_ui_inference.py").exists():
        print("❌ web_ui_inference.py not found. Please run from the project root directory.")
        sys.exit(1)
    
    # Check dependencies
    if not check_dependencies():
        print("\n📦 Installing missing dependencies...")
        if not install_dependencies():
            print("❌ Failed to install dependencies. Please install manually:")
            print("   pip install -r requirements_webui.txt")
            sys.exit(1)
    
    # Check for trained model
    model_paths = [
        "./outputs/checkpoint_step_1000.pt",
        "./outputs/checkpoint_step_500.pt",
        "./outputs/checkpoint_step_0.pt"
    ]
    
    model_path = None
    for path in model_paths:
        if Path(path).exists():
            model_path = path
            print(f"✅ Found trained model: {model_path}")
            break
    
    if not model_path:
        print("⚠️  No trained model found. Will use base model for demo.")
        print("   Train a model first using: python scripts/train_thinker_llm.py")
    
    # Launch web UI
    print("\n🚀 Launching ThinkerLLM Web UI...")
    print("   Access the interface at: http://127.0.0.1:7860")
    print("   Press Ctrl+C to stop the server")
    print("-" * 40)
    
    try:
        # Import and run the web UI
        from web_ui_inference import main as webui_main
        
        # Set up arguments
        sys.argv = ["web_ui_inference.py"]
        if model_path:
            sys.argv.extend(["--model-path", model_path])
        
        # Launch
        webui_main()
        
    except KeyboardInterrupt:
        print("\n👋 Web UI stopped by user")
    except Exception as e:
        print(f"\n❌ Error launching web UI: {e}")
        print("Please check the error message above and try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()
