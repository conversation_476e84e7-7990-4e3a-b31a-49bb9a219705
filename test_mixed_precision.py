#!/usr/bin/env python3
"""
Test script for mixed precision training in ThinkerLLM
"""

import sys
import os
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent))

import torch
import logging
from transformers import AutoTokenizer

# Import ThinkerLLM components
from thinker_llm.models import IntegratedThinkerLLM
from thinker_llm.utils.config import ModelConfig, TrainingConfig
from thinker_llm.training.trainer import ThinkerLLMTrainer

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_mixed_precision_support():
    """Test if mixed precision is supported on this system."""
    logger.info("Testing mixed precision support...")

    # Check CUDA availability
    if not torch.cuda.is_available():
        logger.warning("CUDA not available - mixed precision will be disabled")
        return False

    # Check bf16 support
    bf16_supported = torch.cuda.is_bf16_supported()
    logger.info(f"BF16 support: {bf16_supported}")

    # Check fp16 support (should be available on all modern GPUs)
    fp16_supported = True  # Assume fp16 is supported
    logger.info(f"FP16 support: {fp16_supported}")

    # Get GPU info
    gpu_name = torch.cuda.get_device_name(0)
    logger.info(f"GPU: {gpu_name}")

    return True

def create_test_model_and_config():
    """Create a small test model and config for testing."""
    logger.info("Creating test model and configuration...")

    # Create minimal model config for testing
    model_config = ModelConfig(
        vocab_size=50257,
        hidden_size=128,  # Very small for testing
        num_layers=2,
        num_attention_heads=2,
        intermediate_size=512,
        max_position_embeddings=256,

        # ThinkerModule config
        thinker_hidden_size=192,
        thinker_num_layers=2,
        thinker_num_heads=3,
        thinker_intermediate_size=768,
        thinker_max_length=128,

        # Disable flash attention for compatibility
        use_flash_attention=False,
        gradient_checkpointing=False,
    )

    # Create training config with mixed precision
    training_config = TrainingConfig(
        batch_size=2,
        learning_rate=1e-4,
        num_epochs=1,
        warmup_steps=10,
        max_grad_norm=1.0,
        weight_decay=0.01,

        # Mixed precision settings
        use_amp=True,
        mixed_precision_dtype="bf16",  # Will fallback to fp16 if bf16 not supported

        # Optimization
        optimizer="adamw",
        scheduler="cosine",
        gradient_accumulation_steps=1,

        # Logging
        logging_steps=5,
        eval_steps=10,
        save_steps=20,

        # Data
        max_length=128,
        thinking_ratio=0.3,

        # Paths
        output_dir="./test_outputs",
        logging_dir="./test_logs",
        cache_dir="./test_cache",
    )

    return model_config, training_config

def create_test_data(tokenizer, config):
    """Create minimal test data for training."""
    logger.info("Creating test data...")

    # Create simple test samples with consistent text
    test_text = "What is 2+2? The answer is 4."
    test_samples = [
        {
            "input_ids": tokenizer.encode(test_text, return_tensors="pt")[0],
            "labels": tokenizer.encode(test_text, return_tensors="pt")[0],
            "attention_mask": torch.ones(tokenizer.encode(test_text, return_tensors="pt")[0].shape),
            "requires_thinking": torch.tensor(1.0),  # This sample requires thinking
        },
        {
            "input_ids": tokenizer.encode(test_text, return_tensors="pt")[0],
            "labels": tokenizer.encode(test_text, return_tensors="pt")[0],
            "attention_mask": torch.ones(tokenizer.encode(test_text, return_tensors="pt")[0].shape),
            "requires_thinking": torch.tensor(0.0),  # This sample doesn't require thinking
        }
    ]

    # Pad sequences to same length
    max_len = max(len(sample["input_ids"]) for sample in test_samples)
    max_len = min(max_len, config.max_length)

    for sample in test_samples:
        # Pad input_ids and labels
        input_len = len(sample["input_ids"])
        if input_len < max_len:
            pad_len = max_len - input_len
            sample["input_ids"] = torch.cat([
                sample["input_ids"],
                torch.full((pad_len,), tokenizer.pad_token_id)
            ])
            sample["labels"] = torch.cat([
                sample["labels"],
                torch.full((pad_len,), -100)  # -100 is ignored in loss
            ])
            sample["attention_mask"] = torch.cat([
                sample["attention_mask"],
                torch.zeros(pad_len)
            ])
        else:
            # Truncate if too long
            sample["input_ids"] = sample["input_ids"][:max_len]
            sample["labels"] = sample["labels"][:max_len]
            sample["attention_mask"] = sample["attention_mask"][:max_len]

    return test_samples

def test_mixed_precision_training():
    """Test mixed precision training with a minimal setup."""
    logger.info("Testing mixed precision training...")

    # Check system support
    if not test_mixed_precision_support():
        logger.error("Mixed precision not supported on this system")
        return False

    try:
        # Create test configuration
        model_config, training_config = create_test_model_and_config()

        # Setup tokenizer
        tokenizer = AutoTokenizer.from_pretrained("gpt2")
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token

        # Add special tokens
        special_tokens = ["<|thinking|>", "<|/thinking|>", "<|response|>", "<|/response|>"]
        new_tokens = [token for token in special_tokens if token not in tokenizer.get_vocab()]
        if new_tokens:
            tokenizer.add_tokens(new_tokens)

        # Update model config vocab size
        model_config.vocab_size = len(tokenizer)

        # Create model
        model = IntegratedThinkerLLM(model_config)
        model.resize_token_embeddings(len(tokenizer))

        logger.info(f"Test model created with {sum(p.numel() for p in model.parameters()):,} parameters")

        # Create test data
        test_samples = create_test_data(tokenizer, training_config)

        # Create simple dataloader
        class SimpleDataLoader:
            def __init__(self, samples):
                self.samples = samples

            def __iter__(self):
                for sample in self.samples:
                    # Stack samples into batch format
                    batch = {
                        "input_ids": sample["input_ids"].unsqueeze(0),
                        "labels": sample["labels"].unsqueeze(0),
                        "attention_mask": sample["attention_mask"].unsqueeze(0),
                        "requires_thinking": sample["requires_thinking"].unsqueeze(0),
                    }
                    yield batch

            def __len__(self):
                return len(self.samples)

        train_dataloader = SimpleDataLoader(test_samples)

        # Create output directories
        os.makedirs(training_config.output_dir, exist_ok=True)
        os.makedirs(training_config.logging_dir, exist_ok=True)
        os.makedirs(training_config.cache_dir, exist_ok=True)

        # Create trainer
        trainer = ThinkerLLMTrainer(
            model=model,
            train_dataloader=train_dataloader,
            val_dataloader=None,
            config=training_config,
            model_config=model_config,
        )

        logger.info("Trainer created successfully")
        logger.info(f"Mixed precision enabled: {trainer.use_amp}")
        logger.info(f"Mixed precision dtype: {trainer.mixed_precision_dtype}")

        # Test a few training steps
        logger.info("Running test training steps...")

        model.train()
        for step, batch in enumerate(train_dataloader):
            if step >= 3:  # Only test a few steps
                break

            logger.info(f"Testing step {step + 1}")

            # Move batch to device
            batch = {k: v.to(trainer.device) if isinstance(v, torch.Tensor) else v
                    for k, v in batch.items()}

            # Test forward pass
            if trainer.use_amp:
                with torch.amp.autocast(device_type='cuda', dtype=trainer.mixed_precision_dtype):
                    outputs = model(
                        input_ids=batch["input_ids"],
                        attention_mask=batch["attention_mask"],
                        labels=batch["labels"],
                        return_thoughts=True,
                    )

                    loss_dict = trainer.loss_fn(
                        model_outputs=outputs,
                        labels=batch["labels"],
                        thinking_labels=None,
                        decision_labels=batch["requires_thinking"],
                    )

                    loss = loss_dict["total_loss"]

                # Test backward pass
                if trainer.use_grad_scaling and trainer.scaler is not None:
                    trainer.scaler.scale(loss).backward()
                    trainer.scaler.step(trainer.optimizer)
                    trainer.scaler.update()
                else:
                    loss.backward()
                    trainer.optimizer.step()
            else:
                outputs = model(
                    input_ids=batch["input_ids"],
                    attention_mask=batch["attention_mask"],
                    labels=batch["labels"],
                    return_thoughts=True,
                )

                loss_dict = trainer.loss_fn(
                    model_outputs=outputs,
                    labels=batch["labels"],
                    thinking_labels=None,
                    decision_labels=batch["requires_thinking"],
                )

                loss = loss_dict["total_loss"]
                loss.backward()
                trainer.optimizer.step()

            trainer.optimizer.zero_grad()

            logger.info(f"Step {step + 1} completed - Loss: {loss.item():.4f}")

        logger.info("✅ Mixed precision training test completed successfully!")
        return True

    except Exception as e:
        logger.error(f"❌ Mixed precision training test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    logger.info("🧪 Testing ThinkerLLM Mixed Precision Training")
    logger.info("=" * 60)

    # Run tests
    success = test_mixed_precision_training()

    logger.info("=" * 60)
    if success:
        logger.info("🎉 All tests passed! Mixed precision training is working correctly.")
    else:
        logger.error("❌ Tests failed. Please check the error messages above.")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
