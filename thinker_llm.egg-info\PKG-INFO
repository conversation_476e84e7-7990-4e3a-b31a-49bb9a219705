Metadata-Version: 2.2
Name: thinker-llm
Version: 0.1.0
Summary: Large Language Model with Integrated ThinkerModule for Enhanced Reasoning
Home-page: https://github.com/yourusername/thinker-llm
Author: Your Name
Author-email: <EMAIL>
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: torch>=2.0.0
Requires-Dist: transformers>=4.30.0
Requires-Dist: tokenizers>=0.13.0
Requires-Dist: numpy>=1.21.0
Requires-Dist: scipy>=1.7.0
Requires-Dist: scikit-learn>=1.0.0
Requires-Dist: matplotlib>=3.5.0
Requires-Dist: seaborn>=0.11.0
Requires-Dist: tqdm>=4.64.0
Requires-Dist: wandb>=0.15.0
Requires-Dist: tensorboard>=2.10.0
Requires-Dist: datasets>=2.10.0
Requires-Dist: accelerate>=0.20.0
Requires-Dist: peft>=0.4.0
Requires-Dist: bitsandbytes>=0.39.0
Requires-Dist: flash-attn>=2.0.0
Requires-Dist: einops>=0.6.0
Requires-Dist: omegaconf>=2.3.0
Requires-Dist: hydra-core>=1.3.0
Requires-Dist: pytest>=7.0.0
Requires-Dist: black>=22.0.0
Requires-Dist: isort>=5.10.0
Requires-Dist: flake8>=5.0.0
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: black>=22.0.0; extra == "dev"
Requires-Dist: isort>=5.10.0; extra == "dev"
Requires-Dist: flake8>=5.0.0; extra == "dev"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

# ThinkerLLM: Large Language Model with Integrated Reasoning

ThinkerLLM is a novel architecture that combines traditional autoregressive language modeling with non-autoregressive reasoning capabilities. The system intelligently decides when to engage deeper reasoning processes while maintaining efficiency for simpler tasks.

## 🚀 Key Features

- **Intelligent Decision Making**: Automatically determines when thinking is needed
- **Non-Autoregressive Reasoning**: ThinkerModule processes entire sequences simultaneously
- **Modular Architecture**: Clean separation between reasoning and generation phases
- **Flexible Training**: Support for joint and separate training strategies
- **Efficient Inference**: Only engages thinking when necessary
- **Transparent Reasoning**: Shows thought processes to users when requested

## 🏗️ Architecture Overview

ThinkerLLM consists of three main components:

### 1. DecisionMechanism
- Analyzes input complexity and determines if thinking is needed
- Uses multiple features: input length, vocabulary diversity, question type
- Configurable threshold for thinking decisions

### 2. ThinkerModule
- Non-autoregressive transformer for reasoning
- Generates both visible thoughts and hidden reasoning states
- Single forward pass for efficiency

### 3. MainLLM
- Traditional autoregressive language model
- Integrates reasoning states when available
- Works independently for simple tasks

## 📦 Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/thinker-llm.git
cd thinker-llm

# Install dependencies
pip install -r requirements.txt

# Install the package
pip install -e .
```

## 🚀 Quick Start

### Basic Usage

```python
from thinker_llm import IntegratedThinkerLLM, ThinkerLLMPipeline
from thinker_llm.utils.config import ModelConfig, InferenceConfig
from transformers import AutoTokenizer

# Initialize model and tokenizer
config = ModelConfig()
model = IntegratedThinkerLLM(config)
tokenizer = AutoTokenizer.from_pretrained("gpt2")

# Create inference pipeline
pipeline = ThinkerLLMPipeline(
    model=model,
    tokenizer=tokenizer,
    config=InferenceConfig(show_thinking=True)
)

# Generate response
result = pipeline.generate(
    "Explain how photosynthesis works and why it's important."
)

print("Response:", result["response"])
if result["used_thinking"]:
    print("Thoughts:", result["thoughts"])
```

### Training

```python
from thinker_llm.training import ThinkerLLMTrainer
from thinker_llm.training.data_loader import prepare_dataset
from thinker_llm.utils.config import TrainingConfig

# Prepare data
train_dataset, train_dataloader = prepare_dataset(
    data_path="path/to/training_data.jsonl",
    tokenizer_name="gpt2",
    thinking_ratio=0.3
)

# Configure training
training_config = TrainingConfig(
    batch_size=8,
    learning_rate=5e-5,
    num_epochs=3,
    thinking_ratio=0.3
)

# Train model
trainer = ThinkerLLMTrainer(
    model=model,
    train_dataloader=train_dataloader.get_dataloader(),
    config=training_config
)

trainer.train()
```

## 📊 Training Data Format

ThinkerLLM expects training data in JSONL format with the following structure:

```json
{
    "instruction": "Solve this math problem: 15 + 27 = ?",
    "thinking": "I need to add 15 and 27. Let me do this step by step: 15 + 27 = 42",
    "output": "15 + 27 equals 42.",
    "requires_thinking": true
}
```

### Data Fields

- `instruction`: The input prompt or question
- `thinking`: The reasoning process (optional, for thinking samples)
- `output`: The expected response
- `requires_thinking`: Boolean indicating if thinking is needed

## 🔧 Configuration

### Model Configuration

```python
from thinker_llm.utils.config import ModelConfig

config = ModelConfig(
    # Main LLM settings
    vocab_size=50257,
    hidden_size=768,
    num_layers=12,
    num_attention_heads=12,
    
    # ThinkerModule settings
    thinker_hidden_size=1024,
    thinker_num_layers=8,
    thinker_num_heads=16,
    
    # Decision mechanism settings
    decision_threshold=0.5,
    
    # Projection settings
    projection_type="adaptive"  # "linear", "adaptive", "cross_attention"
)
```

### Training Configuration

```python
from thinker_llm.utils.config import TrainingConfig

config = TrainingConfig(
    batch_size=8,
    learning_rate=5e-5,
    num_epochs=3,
    warmup_steps=1000,
    
    # Loss weights
    llm_loss_weight=1.0,
    thinker_loss_weight=0.5,
    decision_loss_weight=0.3,
    
    # Training strategy
    joint_training=True,
    thinking_ratio=0.3
)
```

## 🎯 Training Strategies

ThinkerLLM supports multiple training strategies:

### 1. Joint Training
Train all components simultaneously:
```python
model.set_training_mode("joint")
```

### 2. Component-Specific Training
Train specific components while freezing others:
```python
# Freeze main LLM, train thinker and decision
model.set_training_mode("frozen_llm")

# Freeze thinker, train LLM and decision
model.set_training_mode("frozen_thinker")

# Freeze decision, train others
model.set_training_mode("frozen_decision")
```

## 🔍 Inference Options

### Basic Generation
```python
result = pipeline.generate("What is the capital of France?")
```

### With Thinking Control
```python
# Force thinking
result = pipeline.generate(
    "Simple question",
    force_thinking=True
)

# Disable thinking
result = pipeline.generate(
    "Complex question",
    disable_thinking=True
)
```

### Batch Processing
```python
results = pipeline.batch_generate([
    "Question 1",
    "Question 2",
    "Question 3"
], batch_size=2)
```

## 📈 Performance Optimization

### For Training
- Use gradient accumulation for larger effective batch sizes
- Enable gradient checkpointing for memory efficiency
- Use mixed precision training with `torch.cuda.amp`

### For Inference
```python
from thinker_llm.utils.model_utils import optimize_model_for_inference

# Optimize model for inference
optimized_model = optimize_model_for_inference(
    model, 
    optimization_level="aggressive"
)
```

## 📁 Project Structure

```
thinker_llm/
├── models/                 # Model architectures
│   ├── thinker_module.py  # Non-autoregressive reasoning
│   ├── main_llm.py        # Autoregressive language model
│   ├── decision_mechanism.py  # Decision router
│   └── integrated_model.py    # Complete system
├── training/              # Training components
│   ├── trainer.py         # Main trainer
│   ├── losses.py          # Loss functions
│   └── data_loader.py     # Data handling
├── inference/             # Inference pipeline
│   ├── pipeline.py        # Main inference pipeline
│   ├── generation.py      # Generation utilities
│   └── postprocessing.py  # Output processing
└── utils/                 # Utilities
    ├── config.py          # Configuration classes
    └── model_utils.py     # Model utilities
```

## 🧪 Examples

See the `examples/` directory for comprehensive usage examples:

- `basic_usage.py`: Simple end-to-end example
- `training_example.py`: Advanced training strategies
- `inference_demo.py`: Inference capabilities demonstration

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📚 Citation

If you use ThinkerLLM in your research, please cite:

```bibtex
@software{thinker_llm,
    title={ThinkerLLM: Large Language Model with Integrated Reasoning},
    author={Your Name},
    year={2024},
    url={https://github.com/yourusername/thinker-llm}
}
```

## 🔗 Related Work

- [Chain-of-Thought Prompting](https://arxiv.org/abs/2201.11903)
- [Self-Consistency](https://arxiv.org/abs/2203.11171)
- [Tree of Thoughts](https://arxiv.org/abs/2305.10601)

## 📞 Support

For questions and support:
- Open an issue on GitHub
- Join our Discord community
- Email: <EMAIL>
