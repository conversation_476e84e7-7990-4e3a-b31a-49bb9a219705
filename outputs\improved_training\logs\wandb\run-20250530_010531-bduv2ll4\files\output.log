INFO:__main__:Starting training...
INFO:thinker_llm.training.trainer:Starting training...
INFO:thinker_llm.training.trainer:Total epochs: 5
INFO:thinker_llm.training.trainer:Total steps: 270
INFO:thinker_llm.training.trainer:train/epoch_loss: 1.7610                                                                                                                             
INFO:thinker_llm.training.trainer:train/epoch_llm_lm_loss: 6.8796
INFO:thinker_llm.training.trainer:train/epoch_llm_total_loss: 6.8796
INFO:thinker_llm.training.trainer:train/epoch_decision_decision_loss: 0.2500
INFO:thinker_llm.training.trainer:train/epoch_decision_calibration_loss: 0.0189
INFO:thinker_llm.training.trainer:train/epoch_decision_efficiency_loss: 0.0900
INFO:thinker_llm.training.trainer:train/epoch_decision_total_loss: 0.2737
INFO:thinker_llm.training.trainer:train/epoch_total_loss: 7.0438
INFO:thinker_llm.training.trainer:Checkpoint saved: ./outputs/improved_training\best_checkpoint.pt                                                                                     
INFO:thinker_llm.training.trainer:val/loss: 6.6588
INFO:thinker_llm.training.trainer:val/llm_lm_loss: 6.4952
INFO:thinker_llm.training.trainer:val/llm_total_loss: 6.4952
INFO:thinker_llm.training.trainer:val/decision_decision_loss: 0.2500
INFO:thinker_llm.training.trainer:val/decision_calibration_loss: 0.0156
INFO:thinker_llm.training.trainer:val/decision_efficiency_loss: 0.0900
INFO:thinker_llm.training.trainer:val/decision_total_loss: 0.2727
INFO:thinker_llm.training.trainer:val/total_loss: 6.6588
INFO:thinker_llm.training.trainer:Epoch 1/5 completed. Train loss: 1.7610
Epoch 2/5:  85%|████████████████████████████████████████████████████████████████████████████████████████████▊                | 46/54 [00:02<00:00, 18.54it/s, loss=1.7420, lr=5.00e-06]INFO:thinker_llm.training.trainer:train/loss: 1.7420
INFO:thinker_llm.training.trainer:train/learning_rate: 0.0000
INFO:thinker_llm.training.trainer:train/global_step: 25.0000
INFO:thinker_llm.training.trainer:train/epoch_loss: 1.7598                                                                                                                             
INFO:thinker_llm.training.trainer:train/epoch_llm_lm_loss: 6.8750
INFO:thinker_llm.training.trainer:train/epoch_llm_total_loss: 6.8750
INFO:thinker_llm.training.trainer:train/epoch_decision_decision_loss: 0.2500
INFO:thinker_llm.training.trainer:train/epoch_decision_calibration_loss: 0.0193
INFO:thinker_llm.training.trainer:train/epoch_decision_efficiency_loss: 0.0900
INFO:thinker_llm.training.trainer:train/epoch_decision_total_loss: 0.2738
INFO:thinker_llm.training.trainer:train/epoch_total_loss: 7.0392
INFO:thinker_llm.training.trainer:Checkpoint saved: ./outputs/improved_training\best_checkpoint.pt                                                                                     
INFO:thinker_llm.training.trainer:val/loss: 6.6498
INFO:thinker_llm.training.trainer:val/llm_lm_loss: 6.4862
INFO:thinker_llm.training.trainer:val/llm_total_loss: 6.4862
INFO:thinker_llm.training.trainer:val/decision_decision_loss: 0.2500
INFO:thinker_llm.training.trainer:val/decision_calibration_loss: 0.0156
INFO:thinker_llm.training.trainer:val/decision_efficiency_loss: 0.0900
INFO:thinker_llm.training.trainer:val/decision_total_loss: 0.2727
INFO:thinker_llm.training.trainer:val/total_loss: 6.6498
INFO:thinker_llm.training.trainer:Epoch 2/5 completed. Train loss: 1.7598
INFO:thinker_llm.training.trainer:train/epoch_loss: 1.7566                                                                                                                             
INFO:thinker_llm.training.trainer:train/epoch_llm_lm_loss: 6.8620
INFO:thinker_llm.training.trainer:train/epoch_llm_total_loss: 6.8620
INFO:thinker_llm.training.trainer:train/epoch_decision_decision_loss: 0.2500
INFO:thinker_llm.training.trainer:train/epoch_decision_calibration_loss: 0.0189
INFO:thinker_llm.training.trainer:train/epoch_decision_efficiency_loss: 0.0900
INFO:thinker_llm.training.trainer:train/epoch_decision_total_loss: 0.2737
INFO:thinker_llm.training.trainer:train/epoch_total_loss: 7.0262
INFO:thinker_llm.training.trainer:Checkpoint saved: ./outputs/improved_training\best_checkpoint.pt                                                                                     
INFO:thinker_llm.training.trainer:val/loss: 6.6332
INFO:thinker_llm.training.trainer:val/llm_lm_loss: 6.4696
INFO:thinker_llm.training.trainer:val/llm_total_loss: 6.4696
INFO:thinker_llm.training.trainer:val/decision_decision_loss: 0.2500
INFO:thinker_llm.training.trainer:val/decision_calibration_loss: 0.0156
INFO:thinker_llm.training.trainer:val/decision_efficiency_loss: 0.0900
INFO:thinker_llm.training.trainer:val/decision_total_loss: 0.2727
INFO:thinker_llm.training.trainer:val/total_loss: 6.6332
INFO:thinker_llm.training.trainer:Epoch 3/5 completed. Train loss: 1.7566
Epoch 4/5:  78%|████████████████████████████████████████████████████████████████████████████████████▊                        | 42/54 [00:02<00:00, 18.38it/s, loss=1.7207, lr=1.00e-05]INFO:thinker_llm.training.trainer:train/loss: 1.7207
INFO:thinker_llm.training.trainer:train/learning_rate: 0.0000
INFO:thinker_llm.training.trainer:train/global_step: 50.0000
INFO:thinker_llm.training.trainer:train/epoch_loss: 1.7488                                                                                                                             
INFO:thinker_llm.training.trainer:train/epoch_llm_lm_loss: 6.8308
INFO:thinker_llm.training.trainer:train/epoch_llm_total_loss: 6.8308
INFO:thinker_llm.training.trainer:train/epoch_decision_decision_loss: 0.2500
INFO:thinker_llm.training.trainer:train/epoch_decision_calibration_loss: 0.0193
INFO:thinker_llm.training.trainer:train/epoch_decision_efficiency_loss: 0.0900
INFO:thinker_llm.training.trainer:train/epoch_decision_total_loss: 0.2738
INFO:thinker_llm.training.trainer:train/epoch_total_loss: 6.9950
INFO:thinker_llm.training.trainer:Checkpoint saved: ./outputs/improved_training\best_checkpoint.pt                                                                                     
INFO:thinker_llm.training.trainer:val/loss: 6.5855
INFO:thinker_llm.training.trainer:val/llm_lm_loss: 6.4218
INFO:thinker_llm.training.trainer:val/llm_total_loss: 6.4218
INFO:thinker_llm.training.trainer:val/decision_decision_loss: 0.2500
INFO:thinker_llm.training.trainer:val/decision_calibration_loss: 0.0156
INFO:thinker_llm.training.trainer:val/decision_efficiency_loss: 0.0900
INFO:thinker_llm.training.trainer:val/decision_total_loss: 0.2727
INFO:thinker_llm.training.trainer:val/total_loss: 6.5855
INFO:thinker_llm.training.trainer:Epoch 4/5 completed. Train loss: 1.7488
INFO:thinker_llm.training.trainer:train/epoch_loss: 1.7363                                                                                                                             
INFO:thinker_llm.training.trainer:train/epoch_llm_lm_loss: 6.7808
INFO:thinker_llm.training.trainer:train/epoch_llm_total_loss: 6.7808
INFO:thinker_llm.training.trainer:train/epoch_decision_decision_loss: 0.2500
INFO:thinker_llm.training.trainer:train/epoch_decision_calibration_loss: 0.0191
INFO:thinker_llm.training.trainer:train/epoch_decision_efficiency_loss: 0.0900
INFO:thinker_llm.training.trainer:train/epoch_decision_total_loss: 0.2737
INFO:thinker_llm.training.trainer:train/epoch_total_loss: 6.9451
INFO:thinker_llm.training.trainer:Checkpoint saved: ./outputs/improved_training\best_checkpoint.pt                                                                                     
INFO:thinker_llm.training.trainer:val/loss: 6.5325
INFO:thinker_llm.training.trainer:val/llm_lm_loss: 6.3689
INFO:thinker_llm.training.trainer:val/llm_total_loss: 6.3689
INFO:thinker_llm.training.trainer:val/decision_decision_loss: 0.2500
INFO:thinker_llm.training.trainer:val/decision_calibration_loss: 0.0156
INFO:thinker_llm.training.trainer:val/decision_efficiency_loss: 0.0900
INFO:thinker_llm.training.trainer:val/decision_total_loss: 0.2727
INFO:thinker_llm.training.trainer:val/total_loss: 6.5325
INFO:thinker_llm.training.trainer:Epoch 5/5 completed. Train loss: 1.7363
INFO:thinker_llm.training.trainer:Training completed!
INFO:thinker_llm.training.trainer:Checkpoint saved: ./outputs/improved_training\final_checkpoint.pt
INFO:__main__:Training completed successfully!
