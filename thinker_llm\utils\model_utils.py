"""
Model utility functions for ThinkerLLM.
"""

import torch
import torch.nn as nn
from typing import Dict, Any, Optional, Union
import os
import json
from pathlib import Path
import logging

from ..models.integrated_model import IntegratedThinkerLLM
from ..utils.config import ModelConfig, TrainingConfig
from transformers import AutoTokenizer


def count_parameters(model: nn.Module, trainable_only: bool = True) -> int:
    """
    Count the number of parameters in a model.
    
    Args:
        model: PyTorch model
        trainable_only: Whether to count only trainable parameters
        
    Returns:
        Number of parameters
    """
    if trainable_only:
        return sum(p.numel() for p in model.parameters() if p.requires_grad)
    else:
        return sum(p.numel() for p in model.parameters())


def get_model_size_info(model: nn.Module) -> Dict[str, Any]:
    """
    Get detailed information about model size.
    
    Args:
        model: PyTorch model
        
    Returns:
        Dictionary with size information
    """
    total_params = count_parameters(model, trainable_only=False)
    trainable_params = count_parameters(model, trainable_only=True)
    
    # Calculate model size in MB (assuming float32)
    model_size_mb = total_params * 4 / (1024 * 1024)
    
    info = {
        "total_parameters": total_params,
        "trainable_parameters": trainable_params,
        "non_trainable_parameters": total_params - trainable_params,
        "model_size_mb": model_size_mb,
        "model_size_gb": model_size_mb / 1024,
    }
    
    # Add component-wise information if it's a ThinkerLLM
    if isinstance(model, IntegratedThinkerLLM):
        info["component_parameters"] = {
            "decision_mechanism": count_parameters(model.decision_mechanism),
            "thinker_module": count_parameters(model.thinker_module),
            "main_llm": count_parameters(model.main_llm),
        }
    
    return info


def save_model(
    model: IntegratedThinkerLLM,
    tokenizer: AutoTokenizer,
    save_path: str,
    model_config: Optional[ModelConfig] = None,
    training_config: Optional[TrainingConfig] = None,
    additional_info: Optional[Dict[str, Any]] = None,
) -> None:
    """
    Save ThinkerLLM model and associated files.
    
    Args:
        model: ThinkerLLM model to save
        tokenizer: Associated tokenizer
        save_path: Directory to save model
        model_config: Model configuration
        training_config: Training configuration
        additional_info: Additional information to save
    """
    save_path = Path(save_path)
    save_path.mkdir(parents=True, exist_ok=True)
    
    # Save model state dict
    model_state_path = save_path / "model.pt"
    torch.save(model.state_dict(), model_state_path)
    
    # Save tokenizer
    tokenizer_path = save_path / "tokenizer"
    tokenizer.save_pretrained(tokenizer_path)
    
    # Save configurations
    if model_config:
        config_path = save_path / "model_config.json"
        model_config.save(str(config_path))
    
    if training_config:
        training_config_path = save_path / "training_config.json"
        training_config.save(str(training_config_path))
    
    # Save model info
    model_info = {
        "model_type": "ThinkerLLM",
        "version": "0.1.0",
        "size_info": get_model_size_info(model),
        "architecture_info": model.get_model_info(),
    }
    
    if additional_info:
        model_info.update(additional_info)
    
    info_path = save_path / "model_info.json"
    with open(info_path, 'w') as f:
        json.dump(model_info, f, indent=2)
    
    logging.info(f"Model saved to {save_path}")


def load_model(
    load_path: str,
    device: str = "auto",
    load_tokenizer: bool = True,
) -> Dict[str, Any]:
    """
    Load ThinkerLLM model and associated files.
    
    Args:
        load_path: Directory containing saved model
        device: Device to load model on
        load_tokenizer: Whether to load tokenizer
        
    Returns:
        Dictionary containing loaded components
    """
    load_path = Path(load_path)
    
    if not load_path.exists():
        raise FileNotFoundError(f"Model directory not found: {load_path}")
    
    # Setup device
    if device == "auto":
        device = "cuda" if torch.cuda.is_available() else "cpu"
    
    # Load configurations
    model_config = None
    training_config = None
    
    model_config_path = load_path / "model_config.json"
    if model_config_path.exists():
        model_config = ModelConfig.load(str(model_config_path))
    
    training_config_path = load_path / "training_config.json"
    if training_config_path.exists():
        training_config = TrainingConfig.load(str(training_config_path))
    
    # Load model
    if model_config is None:
        raise ValueError("Model configuration not found. Cannot initialize model.")
    
    model = IntegratedThinkerLLM(model_config)
    
    model_state_path = load_path / "model.pt"
    if model_state_path.exists():
        state_dict = torch.load(model_state_path, map_location=device)
        model.load_state_dict(state_dict)
    else:
        raise FileNotFoundError(f"Model state dict not found: {model_state_path}")
    
    model.to(device)
    model.eval()
    
    # Load tokenizer
    tokenizer = None
    if load_tokenizer:
        tokenizer_path = load_path / "tokenizer"
        if tokenizer_path.exists():
            tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
        else:
            logging.warning("Tokenizer not found in model directory")
    
    # Load model info
    model_info = None
    info_path = load_path / "model_info.json"
    if info_path.exists():
        with open(info_path, 'r') as f:
            model_info = json.load(f)
    
    result = {
        "model": model,
        "model_config": model_config,
        "training_config": training_config,
        "model_info": model_info,
        "device": device,
    }
    
    if tokenizer:
        result["tokenizer"] = tokenizer
    
    logging.info(f"Model loaded from {load_path}")
    
    return result


def convert_checkpoint_to_model(
    checkpoint_path: str,
    output_path: str,
    tokenizer_name: str = "gpt2",
) -> None:
    """
    Convert a training checkpoint to a standalone model.
    
    Args:
        checkpoint_path: Path to training checkpoint
        output_path: Path to save converted model
        tokenizer_name: Name of tokenizer to save with model
    """
    # Load checkpoint
    checkpoint = torch.load(checkpoint_path, map_location="cpu")
    
    # Extract configurations
    model_config_dict = checkpoint.get("model_config", {})
    training_config_dict = checkpoint.get("config", {})
    
    model_config = ModelConfig(**model_config_dict)
    training_config = TrainingConfig(**training_config_dict)
    
    # Create model and load state
    model = IntegratedThinkerLLM(model_config)
    model.load_state_dict(checkpoint["model_state_dict"])
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
    
    # Add checkpoint info
    additional_info = {
        "checkpoint_info": {
            "global_step": checkpoint.get("global_step", 0),
            "epoch": checkpoint.get("epoch", 0),
            "best_val_loss": checkpoint.get("best_val_loss", float('inf')),
        }
    }
    
    # Save as standalone model
    save_model(
        model=model,
        tokenizer=tokenizer,
        save_path=output_path,
        model_config=model_config,
        training_config=training_config,
        additional_info=additional_info,
    )
    
    logging.info(f"Checkpoint converted and saved to {output_path}")


def compare_models(
    model1_path: str,
    model2_path: str,
) -> Dict[str, Any]:
    """
    Compare two ThinkerLLM models.
    
    Args:
        model1_path: Path to first model
        model2_path: Path to second model
        
    Returns:
        Comparison results
    """
    # Load model info
    info1_path = Path(model1_path) / "model_info.json"
    info2_path = Path(model2_path) / "model_info.json"
    
    info1 = {}
    info2 = {}
    
    if info1_path.exists():
        with open(info1_path, 'r') as f:
            info1 = json.load(f)
    
    if info2_path.exists():
        with open(info2_path, 'r') as f:
            info2 = json.load(f)
    
    comparison = {
        "model1_path": model1_path,
        "model2_path": model2_path,
        "size_comparison": {},
        "architecture_comparison": {},
    }
    
    # Compare sizes
    if "size_info" in info1 and "size_info" in info2:
        size1 = info1["size_info"]
        size2 = info2["size_info"]
        
        comparison["size_comparison"] = {
            "model1_params": size1.get("total_parameters", 0),
            "model2_params": size2.get("total_parameters", 0),
            "param_difference": size2.get("total_parameters", 0) - size1.get("total_parameters", 0),
            "model1_size_mb": size1.get("model_size_mb", 0),
            "model2_size_mb": size2.get("model_size_mb", 0),
        }
    
    # Compare architectures
    if "architecture_info" in info1 and "architecture_info" in info2:
        arch1 = info1["architecture_info"]
        arch2 = info2["architecture_info"]
        
        comparison["architecture_comparison"] = {
            "config_differences": {},
        }
        
        # Compare configs
        config1 = arch1.get("config", {})
        config2 = arch2.get("config", {})
        
        for key in set(config1.keys()) | set(config2.keys()):
            val1 = config1.get(key)
            val2 = config2.get(key)
            if val1 != val2:
                comparison["architecture_comparison"]["config_differences"][key] = {
                    "model1": val1,
                    "model2": val2,
                }
    
    return comparison


def optimize_model_for_inference(
    model: IntegratedThinkerLLM,
    optimization_level: str = "basic",
) -> IntegratedThinkerLLM:
    """
    Optimize model for inference.
    
    Args:
        model: Model to optimize
        optimization_level: Level of optimization ("basic", "aggressive")
        
    Returns:
        Optimized model
    """
    model.eval()
    
    if optimization_level == "basic":
        # Basic optimizations
        # Disable gradient computation
        for param in model.parameters():
            param.requires_grad = False
        
        # Enable inference mode optimizations
        torch.backends.cudnn.benchmark = True
        
    elif optimization_level == "aggressive":
        # More aggressive optimizations
        # Convert to half precision if on GPU
        if next(model.parameters()).is_cuda:
            model = model.half()
        
        # Disable gradient computation
        for param in model.parameters():
            param.requires_grad = False
        
        # Enable optimizations
        torch.backends.cudnn.benchmark = True
        torch.backends.cudnn.deterministic = False
        
        # Try to compile model (PyTorch 2.0+)
        try:
            model = torch.compile(model)
            logging.info("Model compiled for optimization")
        except Exception as e:
            logging.warning(f"Failed to compile model: {e}")
    
    logging.info(f"Model optimized for inference (level: {optimization_level})")
    
    return model
