{"time":"2025-05-30T01:09:43.8238884+07:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"outputs\\improved_training\\logs\\wandb\\run-20250530_010943-zsjkcxqj\\logs\\debug-core.log"}
{"time":"2025-05-30T01:09:43.934613+07:00","level":"INFO","msg":"created new stream","id":"zsjkcxqj"}
{"time":"2025-05-30T01:09:43.934613+07:00","level":"INFO","msg":"stream: started","id":"zsjkcxqj"}
{"time":"2025-05-30T01:09:43.934613+07:00","level":"INFO","msg":"handler: started","stream_id":"zsjkcxqj"}
{"time":"2025-05-30T01:09:43.934613+07:00","level":"INFO","msg":"sender: started","stream_id":"zsjkcxqj"}
{"time":"2025-05-30T01:09:43.934613+07:00","level":"INFO","msg":"writer: Do: started","stream_id":"zsjkcxqj"}
{"time":"2025-05-30T01:09:44.3740317+07:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-05-30T01:09:59.3890837+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T01:10:14.3877591+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T01:10:25.7348731+07:00","level":"INFO","msg":"stream: closing","id":"zsjkcxqj"}
{"time":"2025-05-30T01:10:25.7348731+07:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-05-30T01:10:25.7353764+07:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-05-30T01:10:26.768943+07:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-05-30T01:10:27.1580754+07:00","level":"INFO","msg":"handler: closed","stream_id":"zsjkcxqj"}
{"time":"2025-05-30T01:10:27.1580754+07:00","level":"INFO","msg":"sender: closed","stream_id":"zsjkcxqj"}
{"time":"2025-05-30T01:10:27.1580754+07:00","level":"INFO","msg":"writer: Close: closed","stream_id":"zsjkcxqj"}
{"time":"2025-05-30T01:10:27.1580754+07:00","level":"INFO","msg":"stream: closed","id":"zsjkcxqj"}
