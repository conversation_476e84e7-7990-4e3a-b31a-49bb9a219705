"""
Minimal training script that works around transformers issues.
"""

import torch
import torch.nn as nn
import torch.optim as optim
import logging
import os
import json
from pathlib import Path

from thinker_llm.models import IntegratedThinkerLLM
from thinker_llm.utils.config import ModelConfig
from thinker_llm.training.losses import CombinedLoss

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SimpleTokenizer:
    """Simple tokenizer that doesn't depend on transformers."""
    
    def __init__(self, vocab_size=1000):
        self.vocab_size = vocab_size
        self.pad_token_id = 0
        self.eos_token_id = 1
        self.unk_token_id = 2
        
    def encode(self, text: str, max_length: int = 64):
        """Simple encoding - just convert to character codes."""
        # Convert text to numbers (simplified)
        tokens = [min(ord(c), self.vocab_size - 1) for c in text[:max_length]]
        
        # Pad to max_length
        while len(tokens) < max_length:
            tokens.append(self.pad_token_id)
        
        return tokens[:max_length]
    
    def decode(self, tokens):
        """Simple decoding."""
        return ''.join([chr(min(t, 127)) for t in tokens if t > 2])


def create_simple_dataset():
    """Create a simple dataset without using transformers."""
    logger.info("Creating simple dataset...")
    
    # Simple training samples
    samples = [
        {"text": "What is 2+2? The answer is 4.", "requires_thinking": False},
        {"text": "What is the capital of France? Paris is the capital.", "requires_thinking": False},
        {"text": "Solve 15+27 step by step. First 15+27=42.", "requires_thinking": True},
        {"text": "Explain photosynthesis. Plants use sunlight to make food.", "requires_thinking": True},
    ]
    
    # Repeat samples to create more data
    extended_samples = samples * 25  # 100 total samples
    
    tokenizer = SimpleTokenizer(vocab_size=1000)
    
    dataset = []
    for sample in extended_samples:
        tokens = tokenizer.encode(sample["text"], max_length=32)
        
        # Create labels (same as input for language modeling)
        labels = tokens.copy()
        labels[0] = -100  # Ignore first token in loss
        
        dataset.append({
            "input_ids": torch.tensor(tokens),
            "labels": torch.tensor(labels),
            "requires_thinking": torch.tensor(float(sample["requires_thinking"])),
        })
    
    logger.info(f"Created dataset with {len(dataset)} samples")
    return dataset, tokenizer


def train_minimal_model():
    """Train a minimal model."""
    logger.info("Starting minimal training...")
    
    # Create dataset
    dataset, tokenizer = create_simple_dataset()
    
    # Model configuration
    config = ModelConfig(
        vocab_size=tokenizer.vocab_size,
        hidden_size=128,
        num_layers=2,
        num_attention_heads=4,
        thinker_hidden_size=192,
        thinker_num_layers=2,
        use_flash_attention=False,
        gradient_checkpointing=False,
    )
    
    # Create model
    model = IntegratedThinkerLLM(config)
    loss_fn = CombinedLoss(vocab_size=config.vocab_size)
    optimizer = optim.AdamW(model.parameters(), lr=1e-4)
    
    logger.info(f"Model created with {sum(p.numel() for p in model.parameters()):,} parameters")
    
    # Training loop
    model.train()
    num_epochs = 2
    batch_size = 4
    
    for epoch in range(num_epochs):
        logger.info(f"Epoch {epoch + 1}/{num_epochs}")
        
        total_loss = 0
        num_batches = 0
        
        # Simple batching
        for i in range(0, len(dataset), batch_size):
            batch_samples = dataset[i:i + batch_size]
            
            # Stack tensors
            input_ids = torch.stack([s["input_ids"] for s in batch_samples])
            labels = torch.stack([s["labels"] for s in batch_samples])
            decision_labels = torch.stack([s["requires_thinking"] for s in batch_samples])
            
            # Forward pass
            outputs = model(
                input_ids=input_ids,
                labels=labels,
                return_thoughts=True,
            )
            
            # Compute loss
            losses = loss_fn(
                model_outputs=outputs,
                labels=labels,
                decision_labels=decision_labels,
            )
            
            loss = losses["total_loss"]
            
            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            if num_batches % 5 == 0:
                logger.info(f"  Batch {num_batches}, Loss: {loss.item():.4f}")
        
        avg_loss = total_loss / num_batches
        logger.info(f"Epoch {epoch + 1} completed. Average loss: {avg_loss:.4f}")
    
    logger.info("Training completed!")
    
    # Save model
    save_path = "./minimal_outputs"
    os.makedirs(save_path, exist_ok=True)
    
    torch.save({
        "model_state_dict": model.state_dict(),
        "config": config.__dict__,
        "tokenizer_vocab_size": tokenizer.vocab_size,
    }, os.path.join(save_path, "minimal_model.pt"))
    
    logger.info(f"Model saved to {save_path}")
    
    return model, tokenizer


def test_trained_model(model, tokenizer):
    """Test the trained model."""
    logger.info("Testing trained model...")
    
    model.eval()
    
    test_texts = [
        "What is 2+2?",
        "Explain how plants grow",
        "What color is the sky?",
    ]
    
    with torch.no_grad():
        for text in test_texts:
            logger.info(f"\nInput: {text}")
            
            # Encode
            tokens = tokenizer.encode(text, max_length=16)
            input_ids = torch.tensor(tokens).unsqueeze(0)
            
            # Forward pass
            outputs = model(input_ids=input_ids, return_thoughts=True)
            
            # Get decision info
            decision_info = outputs["decision_info"]
            thinking_prob = decision_info["thinking_probability"].item()
            
            logger.info(f"Thinking probability: {thinking_prob:.3f}")
            logger.info(f"Model output shape: {outputs['logits'].shape}")
            
            # Simple generation (just take argmax of next token)
            next_token_logits = outputs["logits"][0, -1, :]
            next_token = torch.argmax(next_token_logits).item()
            logger.info(f"Next token prediction: {next_token}")


def main():
    """Main function."""
    logger.info("Starting minimal ThinkerLLM training...")
    
    try:
        # Train model
        model, tokenizer = train_minimal_model()
        
        # Test model
        test_trained_model(model, tokenizer)
        
        logger.info("✅ Minimal training completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
