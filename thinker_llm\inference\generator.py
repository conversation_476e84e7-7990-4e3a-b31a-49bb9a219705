"""
ThinkerLLM Generator for Inference
Handles text generation with thinking process visualization and debug information.
"""

import torch
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple, Any
import logging

logger = logging.getLogger(__name__)


class ThinkerGenerator:
    """Generator class for ThinkerLLM inference with debug capabilities."""
    
    def __init__(self, model, tokenizer, device=None):
        """
        Initialize the generator.
        
        Args:
            model: ThinkerLLM model instance
            tokenizer: Tokenizer instance
            device: Device to run inference on
        """
        self.model = model
        self.tokenizer = tokenizer
        self.device = device or torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Special token IDs
        self.thinking_start_id = self.tokenizer.convert_tokens_to_ids("<|thinking|>")
        self.thinking_end_id = self.tokenizer.convert_tokens_to_ids("<|/thinking|>")
        self.response_start_id = self.tokenizer.convert_tokens_to_ids("<|response|>")
        self.response_end_id = self.tokenizer.convert_tokens_to_ids("<|/response|>")
        self.eos_id = self.tokenizer.eos_token_id
        
        logger.info(f"Generator initialized with special tokens:")
        logger.info(f"  thinking_start: {self.thinking_start_id}")
        logger.info(f"  thinking_end: {self.thinking_end_id}")
        logger.info(f"  response_start: {self.response_start_id}")
        logger.info(f"  response_end: {self.response_end_id}")
    
    def generate(self, prompt: str, max_length: int = 512, temperature: float = 0.7,
                top_p: float = 0.9, thinking_budget: float = 0.3, return_thoughts: bool = True,
                debug: bool = False) -> Dict[str, Any]:
        """
        Generate response with optional thinking process.
        
        Args:
            prompt: Input prompt
            max_length: Maximum generation length
            temperature: Sampling temperature
            top_p: Top-p sampling parameter
            thinking_budget: Fraction of tokens to allocate for thinking
            return_thoughts: Whether to return thinking process
            debug: Whether to return debug information
            
        Returns:
            Dictionary containing response, thinking, and debug info
        """
        # try:
        # Tokenize input
        inputs = self.tokenizer(prompt, return_tensors="pt", truncation=True, max_length=512)
        input_ids = inputs["input_ids"].to(self.device)
        attention_mask = inputs["attention_mask"].to(self.device)
        
        # Calculate thinking budget
        thinking_tokens = int(max_length * thinking_budget)
        response_tokens = max_length - thinking_tokens
        
        # Generate with thinking process
        result = self._generate_with_thinking(
            input_ids=input_ids,
            attention_mask=attention_mask,
            max_thinking_tokens=thinking_tokens,
            max_response_tokens=response_tokens,
            temperature=temperature,
            top_p=top_p,
            debug=debug
        )
        
        # Parse and format output
        return self._parse_generation_result(result, prompt, return_thoughts, debug)
            
        # except Exception as e:
        #     logger.error(f"Generation failed: {e}\n{e.with_traceback(None)}")
        #     return {
        #         "response": f"Error during generation: {str(e)}",
        #         "thinking": "",
        #         "error": str(e)
        #     }
    
    def _generate_with_thinking(self, input_ids: torch.Tensor, attention_mask: torch.Tensor,
                               max_thinking_tokens: int, max_response_tokens: int,
                               temperature: float, top_p: float, debug: bool) -> Dict[str, Any]:
        """Generate text with thinking process."""
        
        batch_size = input_ids.size(0)
        device = input_ids.device
        
        # Initialize generation state
        generated_ids = input_ids.clone()
        current_attention_mask = attention_mask.clone()
        
        # Track generation phases
        phase = "thinking"  # Start with thinking
        thinking_tokens_used = 0
        response_tokens_used = 0
        
        # Debug information
        debug_info = {
            "decisions": [],
            "token_stats": {},
            "generation_steps": []
        } if debug else {}
        
        # Add thinking start token
        thinking_start = torch.tensor([[self.thinking_start_id]], device=device)
        generated_ids = torch.cat([generated_ids, thinking_start], dim=1)
        current_attention_mask = torch.cat([current_attention_mask, torch.ones(1, 1, device=device)], dim=1)
        
        with torch.no_grad():
            for step in range(max_thinking_tokens + max_response_tokens):
                # Forward pass
                outputs = self.model(
                    input_ids=generated_ids,
                    attention_mask=current_attention_mask,
                    return_thoughts=True
                )
                
                # Get next token logits
                next_token_logits = outputs['logits'][:, -1, :]
                
                # Apply temperature and top-p sampling
                next_token_id = self._sample_next_token(
                    next_token_logits, temperature, top_p
                )
                
                # Check for phase transitions
                if phase == "thinking":
                    thinking_tokens_used += 1
                    
                    # Check if we should end thinking
                    if (thinking_tokens_used >= max_thinking_tokens or 
                        next_token_id.item() == self.thinking_end_id):
                        
                        # Add thinking end token if not already generated
                        if next_token_id.item() != self.thinking_end_id:
                            next_token_id = torch.tensor([[self.thinking_end_id]], device=device)
                        
                        # Switch to response phase
                        phase = "response"
                        
                        # Add response start token after thinking end
                        generated_ids = torch.cat([generated_ids, next_token_id], dim=1)
                        current_attention_mask = torch.cat([current_attention_mask, torch.ones(1, 1, device=device)], dim=1)
                        
                        response_start = torch.tensor([[self.response_start_id]], device=device)
                        generated_ids = torch.cat([generated_ids, response_start], dim=1)
                        current_attention_mask = torch.cat([current_attention_mask, torch.ones(1, 1, device=device)], dim=1)
                        
                        if debug:
                            debug_info["decisions"].append({
                                "step": step,
                                "action": "phase_transition",
                                "from": "thinking",
                                "to": "response",
                                "thinking_tokens_used": thinking_tokens_used
                            })
                        
                        continue
                
                elif phase == "response":
                    response_tokens_used += 1
                    
                    # Check for end conditions
                    if (response_tokens_used >= max_response_tokens or
                        next_token_id.item() in [self.eos_id, self.response_end_id]):
                        
                        # Add response end token if not EOS
                        if next_token_id.item() != self.response_end_id and next_token_id.item() != self.eos_id:
                            generated_ids = torch.cat([generated_ids, next_token_id], dim=1)
                            current_attention_mask = torch.cat([current_attention_mask, torch.ones(1, 1, device=device)], dim=1)
                        
                        if next_token_id.item() != self.response_end_id:
                            response_end = torch.tensor([[self.response_end_id]], device=device)
                            generated_ids = torch.cat([generated_ids, response_end], dim=1)
                            current_attention_mask = torch.cat([current_attention_mask, torch.ones(1, 1, device=device)], dim=1)
                        
                        break
                
                # Add the generated token
                generated_ids = torch.cat([generated_ids, next_token_id], dim=1)
                current_attention_mask = torch.cat([current_attention_mask, torch.ones(1, 1, device=device)], dim=1)
                
                # Debug information
                if debug:
                    debug_info["generation_steps"].append({
                        "step": step,
                        "phase": phase,
                        "token_id": next_token_id.item(),
                        "token": self.tokenizer.decode([next_token_id.item()]),
                        "thinking_tokens_used": thinking_tokens_used,
                        "response_tokens_used": response_tokens_used
                    })
        
        # Compile results
        result = {
            "generated_ids": generated_ids,
            "thinking_tokens_used": thinking_tokens_used,
            "response_tokens_used": response_tokens_used,
            "total_tokens": generated_ids.size(1)
        }
        
        if debug:
            debug_info["token_stats"] = {
                "thinking_tokens": thinking_tokens_used,
                "response_tokens": response_tokens_used,
                "total_generated": generated_ids.size(1) - input_ids.size(1),
                "input_tokens": input_ids.size(1)
            }
            result["debug_info"] = debug_info
        
        return result
    
    def _sample_next_token(self, logits: torch.Tensor, temperature: float, top_p: float) -> torch.Tensor:
        """Sample next token using temperature and top-p sampling."""
        
        # Apply temperature
        if temperature != 1.0:
            logits = logits / temperature
        
        # Apply top-p sampling
        if top_p < 1.0:
            sorted_logits, sorted_indices = torch.sort(logits, descending=True)
            cumulative_probs = torch.cumsum(F.softmax(sorted_logits, dim=-1), dim=-1)
            
            # Remove tokens with cumulative probability above the threshold
            sorted_indices_to_remove = cumulative_probs > top_p
            sorted_indices_to_remove[..., 1:] = sorted_indices_to_remove[..., :-1].clone()
            sorted_indices_to_remove[..., 0] = 0
            
            # Scatter sorted tensors to original indexing
            indices_to_remove = sorted_indices_to_remove.scatter(1, sorted_indices, sorted_indices_to_remove)
            logits[indices_to_remove] = float('-inf')
        
        # Sample from the distribution
        probs = F.softmax(logits, dim=-1)
        next_token = torch.multinomial(probs, num_samples=1)
        
        return next_token
    
    def _parse_generation_result(self, result: Dict[str, Any], original_prompt: str,
                                return_thoughts: bool, debug: bool) -> Dict[str, Any]:
        """Parse the generation result and extract thinking and response."""
        
        generated_ids = result["generated_ids"]
        full_text = self.tokenizer.decode(generated_ids[0], skip_special_tokens=False)
        
        # Extract thinking and response sections
        thinking_text = ""
        response_text = ""
        
        # Find thinking section
        thinking_start_token = "<|thinking|>"
        thinking_end_token = "<|/thinking|>"
        response_start_token = "<|response|>"
        response_end_token = "<|/response|>"
        
        if thinking_start_token in full_text and thinking_end_token in full_text:
            thinking_start = full_text.find(thinking_start_token) + len(thinking_start_token)
            thinking_end = full_text.find(thinking_end_token)
            if thinking_start < thinking_end:
                thinking_text = full_text[thinking_start:thinking_end].strip()
        
        # Find response section
        if response_start_token in full_text:
            response_start = full_text.find(response_start_token) + len(response_start_token)
            response_end = full_text.find(response_end_token)
            if response_end == -1:
                response_end = len(full_text)
            response_text = full_text[response_start:response_end].strip()
        
        # Compile final result
        parsed_result = {
            "response": response_text,
            "full_text": full_text,
            "token_stats": {
                "thinking_tokens": result["thinking_tokens_used"],
                "response_tokens": result["response_tokens_used"],
                "total_tokens": result["total_tokens"]
            }
        }
        
        if return_thoughts:
            parsed_result["thinking"] = thinking_text
        
        if debug and "debug_info" in result:
            parsed_result.update(result["debug_info"])
        
        return parsed_result
