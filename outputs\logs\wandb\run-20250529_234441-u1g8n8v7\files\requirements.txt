absl-py==2.1.0
accelerate==1.4.0
aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.11.13
aiosignal==1.3.2
airportsdata==20250224
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
anyio==4.8.0
argbind==0.3.9
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
astor==0.8.1
asttokens==3.0.0
async-lru==2.0.4
attrs==25.1.0
audioread==3.0.1
babel==2.17.0
backoff==2.2.1
beautifulsoup4==4.13.3
bitsandbytes==0.45.3
black==25.1.0
blake3==1.0.4
bleach==6.2.0
blobfile==3.0.0
cachetools==5.5.2
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
cloudpickle==3.1.1
colorama==0.4.6
comm==0.2.2
compressed-tensors==0.9.2
contourpy==1.3.1
coverage==7.8.2
cut-cross-entropy==25.1.1
cycler==0.12.1
datasets==3.6.0
debugpy==1.8.12
decorator==5.2.1
defusedxml==0.7.1
depyf==0.18.0
descript-audiotools==0.7.4
descript-audio-codec==1.0.0
diffusers==0.32.2
dill==0.3.8
diskcache==5.6.3
distro==1.9.0
dnspython==2.7.0
docker-pycreds==0.4.0
docstring_parser==0.16
einops==0.8.1
einx==0.3.0
email_validator==2.2.0
encodec==0.1.1
evaluate==0.4.3
executing==2.2.0
fairscale==0.4.13
fastapi==0.115.11
fastapi-cli==0.0.7
fastchat==0.1.0
fastjsonschema==2.21.1
ffmpy==0.5.0
filelock==3.17.0
fire==0.7.0
flake8==7.2.0
flash_attn==2.7.4
Flask==2.2.5
flatten-dict==0.4.2
fonttools==4.56.0
fqdn==1.5.1
frozendict==2.4.6
frozenlist==1.5.0
fsspec==2024.12.0
future==1.0.0
gguf==0.10.0
gitdb==4.0.12
GitPython==3.1.44
googleapis-common-protos==1.70.0
google-ai-generativelanguage==0.6.15
google-api-core==2.25.0rc0
google-api-python-client==2.169.0
google-auth==2.39.0
google-auth-httplib2==0.2.0
google-generativeai==0.8.5
GPUtil==1.4.0
gradio==5.27.0
gradio_client==1.9.0
graphviz==0.20.3
groovy==0.1.2
groq==0.23.1
grpcio==1.71.0
grpcio-status==1.71.0
h11==0.14.0
hf_transfer==0.1.9
hf-xet==1.0.3
hiddenlayer==0.3
httpcore==1.0.7
httplib2==0.22.0
httptools==0.6.4
httpx==0.28.1
huggingface-hub==0.30.2
hydra-core==1.3.2
idna==3.10
importlib_metadata==8.6.1
importlib_resources==6.5.2
inflect==7.5.0
iniconfig==2.0.0
interegular==0.3.3
ipykernel==6.29.5
ipython==8.32.0
ipywebrtc==0.6.0
ipywidgets==8.1.5
isoduration==20.11.0
isort==6.0.1
itsdangerous==2.2.0
jedi==0.19.2
Jinja2==3.1.6
jiter==0.9.0
joblib==1.4.2
json5==0.10.0
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
julius==0.2.7
jupyterlab==4.4.1
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.3
jupyterlab_widgets==3.0.13
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyter-events==0.12.0
jupyter-lsp==2.2.5
jupyter_server==2.15.0
jupyter_server_terminals==0.5.3
kagglehub==0.3.10
kaleido==0.2.1
kiwisolver==1.4.8
langdetect==1.0.9
lark==1.2.2
lazy_loader==0.4
librosa==0.10.2.post1
llama_cpp_python==0.3.8
llama_tts==0.1.0
llvmlite==0.44.0
lm-format-enforcer==0.10.11
loguru==0.7.3
lxml==5.3.1
Markdown==3.7
markdown2==2.5.3
markdown-it-py==3.0.0
MarkupSafe==2.1.5
matplotlib==3.10.1
matplotlib-inline==0.1.7
maturin==1.8.3
mccabe==0.7.0
mdurl==0.1.2
memory-profiler==0.61.0
mistral_common==1.5.4
mistune==3.1.2
mpmath==1.3.0
msgpack==1.1.0
msgspec==0.19.0
multidict==6.1.0
multiprocess==0.70.16
mypy_extensions==1.1.0
narwhals==1.34.1
nbclient==0.10.2
nbconvert==7.16.6
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.3
ninja==********
nltk==3.9.1
notebook_shim==0.2.4
numba==0.61.0
numpy==1.26.4
omegaconf==2.3.0
openai==1.68.2
opencv-python-headless==*********
optimum==1.24.0
orjson==3.10.16
orpheus-speech==0.1.0
outlines==0.1.11
outlines_core==0.1.26
overrides==7.7.0
packaging==24.2
pandas==2.2.3
pandocfilters==1.5.1
parler_tts==0.2.2
parso==0.8.4
partial-json-parser==*******.post5
pathspec==0.12.1
peft==0.14.0
pickleshare==0.7.5
pillow==11.0.0
pip==25.0
platformdirs==4.3.6
plotly==6.0.1
pluggy==1.5.0
polars==1.25.2
pooch==1.8.2
prometheus_client==0.21.1
prometheus-fastapi-instrumentator==7.1.0
prompt_toolkit==3.0.50
propcache==0.3.0
protobuf==3.20.3
proto-plus==1.26.1
psutil==7.0.0
pure_eval==0.2.3
pyaml==25.1.0
pyarrow==19.0.1
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycodestyle==2.13.0
pycountry==24.6.1
pycparser==2.22
pycryptodomex==3.21.0
pydantic==2.10.6
pydantic_core==2.27.2
pydub==0.25.1
pyflakes==3.3.2
Pygments==2.19.1
pyloudnorm==0.1.1
pyparsing==3.2.1
pystoi==0.4.1
pytest==8.3.5
pytest-cov==6.1.1
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-json-logger==3.2.1
python-multipart==0.0.20
pytz==2025.1
pywin32==309
pywinpty==2.0.15
PyYAML==6.0.2
pyzmq==26.2.1
py-cpuinfo==9.0.0
randomname==0.2.1
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rich==13.9.4
rich-toolkit==0.13.2
rpds-py==0.23.1
rsa==4.9.1
ruff==0.11.7
safehttpx==0.1.6
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.15.2
seaborn==0.13.2
semantic-version==2.10.0
Send2Trash==1.8.3
sentencepiece==0.2.0
sentry-sdk==2.23.1
setproctitle==1.3.5
setuptools==75.8.0
shellingham==1.5.4
shortuuid==1.0.13
shtab==1.7.1
six==1.17.0
smmap==5.0.2
snac==1.2.1
sniffio==1.3.1
sounddevice==0.5.0
soundfile==0.13.1
soupsieve==2.6
soxr==0.5.0.post1
sphn==0.1.8
stack-data==0.6.3
starlette==0.46.1
sympy==1.13.3
tenacity==9.1.2
tensorboard==2.19.0
tensorboard-data-server==0.7.2
termcolor==2.5.0
terminado==0.18.1
threadpoolctl==3.5.0
tiktoken==0.9.0
tinycss2==1.4.0
tokenizers==0.21.1
tomlkit==0.13.2
torch==2.7.0+cu126
torchao==0.9.0
torchaudio==2.7.0+cu126
torchinfo==1.8.0
torchtune==0.5.0
torchvision==0.22.0+cu126
torchviz==0.0.3
torch-stoi==0.2.3
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
transformers==4.51.3
triton==3.2.0
triton-windows==3.2.0.post13
trl==0.15.2
typeguard==4.4.2
typer==0.15.2
types-python-dateutil==2.9.0.20241206
typing_extensions==4.12.2
tyro==0.9.16
tzdata==2025.1
underthesea_core==2.0.0a0
unsloth==2025.5.7
unsloth_zoo==2025.5.8
uritemplate==4.1.1
uri-template==1.3.0
urllib3==2.3.0
uroman==*******
uvicorn==0.34.0
wandb==0.19.8
watchfiles==1.0.4
wcwidth==0.2.13
webcolors==24.11.1
webencodings==0.5.1
websockets==15.0.1
websocket-client==1.8.0
Werkzeug==3.1.3
wheel==0.45.1
widgetsnbextension==4.0.13
win32_setctime==1.2.0
xformers==0.0.30
xgrammar==0.1.16
xxhash==3.5.0
yarl==1.18.3
zipp==3.21.0
zstandard==0.23.0
thinker-llm==0.1.0
thinker-llm==0.1.0
autocommand==2.2.2
backports.tarfile==1.2.0
importlib_metadata==8.0.0
inflect==7.3.1
jaraco.collections==5.1.0
jaraco.context==5.3.0
jaraco.functools==4.0.1
jaraco.text==3.12.1
more-itertools==10.3.0
packaging==24.2
platformdirs==4.2.2
tomli==2.0.1
typeguard==4.3.0
typing_extensions==4.12.2
wheel==0.43.0
zipp==3.19.2
