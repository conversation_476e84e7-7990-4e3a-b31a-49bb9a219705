#!/usr/bin/env python3
"""
Demo script for ThinkerLLM inference capabilities
Shows various examples of the model's thinking process
"""

import sys
import os
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent))

import torch
import json
from transformers import AutoTokenizer
import logging

# Import ThinkerLLM components
from thinker_llm.models import IntegratedThinkerLLM
from thinker_llm.utils.config import ModelConfig
from thinker_llm.inference.generator import ThinkerGenerator

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ThinkerDemo:
    """Demo class for showcasing ThinkerLLM capabilities."""
    
    def __init__(self, model_path=None):
        """Initialize the demo with model and tokenizer."""
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.load_model(model_path)
    
    def load_model(self, model_path=None):
        """Load model and tokenizer."""
        try:
            logger.info("Loading ThinkerLLM for demo...")
            # Setup tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained("gpt2")
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Add special tokens
            special_tokens = ["<|thinking|>", "<|/thinking|>", "<|response|>", "<|/response|>"]
            new_tokens = [token for token in special_tokens if token not in self.tokenizer.get_vocab()]
            if new_tokens:
                self.tokenizer.add_tokens(new_tokens)
                
            checkpoint = {}
            # Load trained weights if available
            if model_path and os.path.exists(model_path):
                logger.info(f"Loading trained model from {model_path}")
                checkpoint = torch.load(model_path, map_location=self.device)                
                logger.info("✅ Trained model loaded successfully")
            else:
                logger.info("⚠️  Using base model (not trained)")
            
            # Create model
            if "model_config" in checkpoint:
                model_config = ModelConfig(**checkpoint['model_config'])
            else:
                model_config = ModelConfig()
            model_config.vocab_size = len(self.tokenizer)
            model_config.use_flash_attention = False
            
            self.model = IntegratedThinkerLLM(model_config)
            self.model.resize_token_embeddings(len(self.tokenizer))
            
            if 'model_state_dict' in checkpoint:
                self.model.load_state_dict(checkpoint['model_state_dict'])
            else:
                self.model.load_state_dict(checkpoint)
            
            
            
            
            
            
            
            
            self.model.to(self.device)
            self.model.eval()
            
            # Create generator
            self.generator = ThinkerGenerator(self.model, self.tokenizer)
            
            logger.info(f"Demo initialized on {self.device}")
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise
    
    def run_demo_examples(self):
        """Run a series of demo examples."""
        
        examples = [
            {
                "name": "Mathematical Reasoning",
                "prompt": "What is 15 * 23? Show your work step by step.",
                "params": {"temperature": 0.3, "thinking_budget": 0.4}
            },
            {
                "name": "Creative Writing",
                "prompt": "Write a short story about a robot learning to paint.",
                "params": {"temperature": 0.8, "thinking_budget": 0.3}
            },
            {
                "name": "Problem Solving",
                "prompt": "How would you design a system to reduce traffic congestion in a city?",
                "params": {"temperature": 0.6, "thinking_budget": 0.5}
            },
            {
                "name": "Factual Question",
                "prompt": "What are the main causes of climate change and their relative impacts?",
                "params": {"temperature": 0.4, "thinking_budget": 0.3}
            },
            {
                "name": "Logical Puzzle",
                "prompt": "If all roses are flowers, and some flowers are red, can we conclude that some roses are red?",
                "params": {"temperature": 0.2, "thinking_budget": 0.6}
            }
        ]
        
        print("🧠 ThinkerLLM Demo - Showcasing Thinking Process")
        print("=" * 60)
        
        for i, example in enumerate(examples, 1):
            print(f"\n📝 Example {i}: {example['name']}")
            print("-" * 40)
            print(f"Prompt: {example['prompt']}")
            print()
            
            # Generate response
            result = self.generator.generate(
                prompt=example['prompt'],
                max_length=400,
                return_thoughts=True,
                debug=True,
                **example['params']
            )
            
            # Display thinking process
            if result.get('thinking'):
                print("🤔 Thinking Process:")
                print(f"   {result['thinking']}")
                print()
            
            # Display response
            print("💡 Response:")
            print(f"   {result['response']}")
            print()
            
            # Display stats
            stats = result.get('token_stats', {})
            print("📊 Generation Stats:")
            print(f"   Thinking tokens: {stats.get('thinking_tokens', 0)}")
            print(f"   Response tokens: {stats.get('response_tokens', 0)}")
            print(f"   Total tokens: {stats.get('total_tokens', 0)}")
            
            print("\n" + "=" * 60)
            
            # Pause between examples
            input("Press Enter to continue to next example...")
    
    def interactive_mode(self):
        """Run interactive demo mode."""
        print("\n🎮 Interactive Mode")
        print("Type your prompts and see ThinkerLLM's thinking process!")
        print("Commands: 'quit' to exit, 'params' to adjust parameters")
        print("-" * 50)
        
        # Default parameters
        params = {
            "temperature": 0.7,
            "top_p": 0.9,
            "thinking_budget": 0.3,
            "max_length": 512
        }
        
        while True:
            try:
                prompt = input("\n💭 Your prompt: ").strip()
                
                if prompt.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                
                if prompt.lower() == 'params':
                    print("\n⚙️ Current parameters:")
                    for key, value in params.items():
                        print(f"   {key}: {value}")
                    
                    print("\nEnter new values (press Enter to keep current):")
                    for key in params:
                        new_value = input(f"   {key} [{params[key]}]: ").strip()
                        if new_value:
                            try:
                                params[key] = float(new_value) if '.' in new_value else int(new_value)
                            except ValueError:
                                print(f"   Invalid value for {key}, keeping {params[key]}")
                    continue
                
                if not prompt:
                    continue
                
                print("\n🔄 Generating response...")
                
                # Generate response
                result = self.generator.generate(
                    prompt=prompt,
                    return_thoughts=True,
                    debug=False,
                    **params
                )
                print(result)
                # Display results
                if result.get('thinking'):
                    print(f"\n🤔 Thinking: {result['thinking']}")
                
                print(f"\n💡 Response: {result['response']}")
                
                # Show stats
                stats = result.get('token_stats', {})
                print(f"\n📊 Stats: {stats.get('thinking_tokens', 0)} thinking + {stats.get('response_tokens', 0)} response tokens")
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"\n❌ Error: {e}")

def main():
    """Main demo function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="ThinkerLLM Demo")
    parser.add_argument("--model-path", type=str, help="Path to trained model checkpoint")
    parser.add_argument("--mode", choices=["examples", "interactive", "both"], 
                       default="both", help="Demo mode to run")
    
    args = parser.parse_args()
    
    # Initialize demo
    demo = ThinkerDemo(model_path=args.model_path)
    
    # Run demo based on mode
    if args.mode in ["examples", "both"]:
        demo.run_demo_examples()
    
    if args.mode in ["interactive", "both"]:
        demo.interactive_mode()

if __name__ == "__main__":
    main()
