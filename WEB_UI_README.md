# 🧠 ThinkerLLM Web UI

A comprehensive web interface for interacting with ThinkerLLM, featuring real-time inference, thinking process visualization, and detailed debug information.

## 🚀 Quick Start

### 1. Install Dependencies
```bash
# Install web UI dependencies
pip install gradio fastapi uvicorn

# Or install from requirements file
pip install -r requirements_webui.txt
```

### 2. Launch the Web UI

#### Option A: Simple UI (Recommended for first-time users)
```bash
python simple_webui.py
```

#### Option B: Full-Featured UI
```bash
python web_ui_inference.py
```

#### Option C: Use the launcher script
```bash
python launch_webui.py
```

### 3. Access the Interface
Open your browser and navigate to: `http://127.0.0.1:7860`

## 📋 Features

### 🎯 Core Features
- **Real-time Inference**: Generate responses with ThinkerLLM in real-time
- **Thinking Process Visualization**: See the model's internal reasoning
- **Debug Information**: Detailed generation statistics and model info
- **Parameter Control**: Adjust temperature, top-p, thinking budget, and more
- **Model Management**: Load different checkpoints on-the-fly

### 🔧 Advanced Features
- **Multiple Generation Modes**: Standard and thinking-enhanced generation
- **Token Statistics**: Track thinking vs response token usage
- **Generation History**: Review previous interactions
- **Export Capabilities**: Save conversations and debug info
- **Performance Monitoring**: Real-time generation speed metrics

## 🎮 Interface Overview

### Main Components

1. **Input Panel**
   - Prompt text area
   - Generation parameters (temperature, top-p, etc.)
   - Thinking budget slider
   - Generate and clear buttons

2. **Output Panel**
   - Generated response display
   - Thinking process visualization
   - Token usage statistics

3. **Debug Panel**
   - Detailed generation metrics
   - Model information
   - Performance statistics

4. **Model Management**
   - Load trained checkpoints
   - Model status indicator
   - Configuration display

### Parameter Guide

| Parameter | Range | Description |
|-----------|-------|-------------|
| **Max Length** | 50-1024 | Maximum tokens to generate |
| **Temperature** | 0.1-2.0 | Controls randomness (lower = more focused) |
| **Top-p** | 0.1-1.0 | Nucleus sampling threshold |
| **Thinking Budget** | 0.1-0.8 | Fraction of tokens for thinking process |

## 🔍 Demo Examples

### Mathematical Reasoning
```
Prompt: "What is 15 * 23? Show your work step by step."
Thinking Budget: 0.4
Temperature: 0.3
```

### Creative Writing
```
Prompt: "Write a short story about a robot learning to paint."
Thinking Budget: 0.3
Temperature: 0.8
```

### Problem Solving
```
Prompt: "How would you design a system to reduce traffic congestion?"
Thinking Budget: 0.5
Temperature: 0.6
```

## 🛠️ Development and Debugging

### Running Tests
```bash
# Test all components
python test_webui.py

# Test specific functionality
python demo_inference.py --mode interactive
```

### Debug Mode
Enable debug mode in the web UI to see:
- Token-by-token generation steps
- Decision points in the thinking process
- Attention weights (if available)
- Performance bottlenecks

### Loading Trained Models
```bash
# Load specific checkpoint
python web_ui_inference.py --model-path ./outputs/checkpoint_step_1000.pt

# Auto-detect latest checkpoint
python launch_webui.py
```

## 📊 Understanding the Output

### Thinking Process
The thinking section shows the model's internal reasoning:
```
🤔 Thinking: Let me break this down step by step. First, I need to...
```

### Response
The final answer or response:
```
💡 Response: Based on my analysis, the answer is...
```

### Debug Information
```json
{
  "generation_time": "2.34s",
  "token_stats": {
    "thinking_tokens": 45,
    "response_tokens": 67,
    "total_tokens": 112
  },
  "model_info": {
    "device": "cuda",
    "vocab_size": 50261,
    "model_parameters": "369,041,162"
  }
}
```

## 🔧 Customization

### Adding Custom Prompts
Create preset prompts by modifying the interface:
```python
preset_prompts = [
    "Explain quantum computing in simple terms",
    "Write a Python function to sort a list",
    "Analyze the pros and cons of renewable energy"
]
```

### Styling
Customize the interface appearance:
```python
interface = gr.Blocks(
    title="My ThinkerLLM",
    theme=gr.themes.Soft(),
    css="custom.css"
)
```

## 🚨 Troubleshooting

### Common Issues

1. **Model Loading Errors**
   ```
   Solution: Check model path and ensure checkpoint file exists
   ```

2. **CUDA Out of Memory**
   ```
   Solution: Reduce max_length or use CPU mode
   ```

3. **Slow Generation**
   ```
   Solution: Use GPU, reduce thinking budget, or smaller model
   ```

4. **Import Errors**
   ```
   Solution: Ensure all dependencies are installed and paths are correct
   ```

### Performance Tips

- Use GPU for faster inference
- Adjust thinking budget based on task complexity
- Lower temperature for more focused responses
- Use smaller max_length for faster generation

## 📝 File Structure

```
├── web_ui_inference.py          # Full-featured web UI
├── simple_webui.py              # Simplified web UI
├── launch_webui.py              # Launcher script
├── demo_inference.py            # Demo and testing script
├── test_webui.py                # Component tests
├── requirements_webui.txt       # Web UI dependencies
├── thinker_llm/inference/
│   └── generator.py             # Web UI generator class
└── WEB_UI_README.md            # This file
```

## 🤝 Contributing

To contribute to the web UI:

1. Test your changes with `python test_webui.py`
2. Ensure compatibility with both simple and full UI
3. Update documentation for new features
4. Follow the existing code style

## 📄 License

This web UI is part of the ThinkerLLM project and follows the same license terms.

---

**Happy thinking! 🧠✨**
