"""
CUDA diagnostic script to identify training issues.
"""
import sys
import os
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent.parent))
import torch
import logging
import traceback
from transformers import AutoTokenizer

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_cuda_basic():
    """Test basic CUDA functionality."""
    logger.info("Testing basic CUDA functionality...")

    try:
        if torch.cuda.is_available():
            logger.info(f"✅ CUDA available: {torch.cuda.get_device_name()}")
            logger.info(f"✅ CUDA version: {torch.version.cuda}")
            logger.info(f"✅ PyTorch version: {torch.__version__}")

            device_props = torch.cuda.get_device_properties(0)
            total_memory = device_props.total_memory / 1e9
            logger.info(f"✅ GPU memory: {total_memory:.1f} GB")

            return True
        else:
            logger.warning("❌ CUDA not available")
            return False
    except Exception as e:
        logger.error(f"❌ CUDA basic test failed: {e}")
        return False


def test_cuda_operations():
    """Test CUDA tensor operations."""
    logger.info("Testing CUDA tensor operations...")

    try:
        # Test small operations first
        a = torch.randn(10, 10).cuda()
        b = torch.randn(10, 10).cuda()
        c = torch.mm(a, b)
        logger.info("✅ Small matrix multiplication: OK")

        # Test larger operations
        a = torch.randn(1000, 1000).cuda()
        b = torch.randn(1000, 1000).cuda()
        c = torch.mm(a, b)
        logger.info("✅ Large matrix multiplication: OK")

        # Clean up
        del a, b, c
        torch.cuda.empty_cache()

        return True
    except Exception as e:
        logger.error(f"❌ CUDA operations test failed: {e}")
        traceback.print_exc()
        return False


def test_memory_allocation():
    """Test GPU memory allocation patterns."""
    logger.info("Testing GPU memory allocation...")

    try:
        # Check initial memory
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            initial_memory = torch.cuda.memory_allocated() / 1e6
            logger.info(f"Initial GPU memory: {initial_memory:.1f} MB")

            # Allocate progressively larger tensors
            sizes = [100, 500, 1000, 2000]
            for size in sizes:
                try:
                    tensor = torch.randn(size, size).cuda()
                    current_memory = torch.cuda.memory_allocated() / 1e6
                    logger.info(f"✅ Allocated {size}x{size} tensor, memory: {current_memory:.1f} MB")
                    del tensor
                    torch.cuda.empty_cache()
                except Exception as e:
                    logger.error(f"❌ Failed to allocate {size}x{size} tensor: {e}")
                    return False

            return True
        else:
            logger.info("Skipping memory test (no CUDA)")
            return True
    except Exception as e:
        logger.error(f"❌ Memory allocation test failed: {e}")
        return False


def test_model_creation():
    """Test creating a small ThinkerLLM model."""
    logger.info("Testing ThinkerLLM model creation...")

    try:
        from thinker_llm.models import IntegratedThinkerLLM
        from thinker_llm.utils.config import ModelConfig

        # Very small model for testing
        config = ModelConfig(
            vocab_size=1000,
            hidden_size=64,
            num_layers=2,
            num_attention_heads=2,
            thinker_hidden_size=96,
            thinker_num_layers=2,
            use_flash_attention=False,
        )

        model = IntegratedThinkerLLM(config)
        logger.info("✅ Model creation: OK")

        # Test moving to CUDA
        if torch.cuda.is_available():
            model = model.cuda()
            logger.info("✅ Model to CUDA: OK")

        # Test forward pass
        batch_size = 2
        seq_len = 10
        input_ids = torch.randint(0, config.vocab_size, (batch_size, seq_len))

        if torch.cuda.is_available():
            input_ids = input_ids.cuda()

        with torch.no_grad():
            outputs = model(input_ids=input_ids)

        logger.info("✅ Model forward pass: OK")

        # Clean up
        del model, outputs, input_ids
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        return True
    except Exception as e:
        logger.error(f"❌ Model creation test failed: {e}")
        traceback.print_exc()
        return False


def test_training_step():
    """Test a single training step."""
    logger.info("Testing training step...")

    try:
        from thinker_llm.models import IntegratedThinkerLLM
        from thinker_llm.utils.config import ModelConfig
        from thinker_llm.training.losses import CombinedLoss

        # Very small model
        config = ModelConfig(
            vocab_size=1000,
            hidden_size=64,
            num_layers=2,
            num_attention_heads=2,
            thinker_hidden_size=96,
            thinker_num_layers=2,
            use_flash_attention=False,
        )

        model = IntegratedThinkerLLM(config)
        if torch.cuda.is_available():
            model = model.cuda()

        # Create loss function
        loss_fn = CombinedLoss(vocab_size=config.vocab_size)

        # Create dummy data
        batch_size = 2
        seq_len = 10
        input_ids = torch.randint(0, config.vocab_size, (batch_size, seq_len))
        labels = torch.randint(0, config.vocab_size, (batch_size, seq_len))

        if torch.cuda.is_available():
            input_ids = input_ids.cuda()
            labels = labels.cuda()

        # Forward pass
        outputs = model(input_ids=input_ids, labels=labels, return_thoughts=True)
        logger.info("✅ Forward pass: OK")

        # Loss computation
        decision_labels = torch.ones(batch_size)
        if torch.cuda.is_available():
            decision_labels = decision_labels.cuda()

        losses = loss_fn(
            model_outputs=outputs,
            labels=labels,
            decision_labels=decision_labels,
        )
        logger.info("✅ Loss computation: OK")

        # Backward pass
        losses["total_loss"].backward()
        logger.info("✅ Backward pass: OK")

        # Clean up
        del model, outputs, losses, input_ids, labels
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        return True
    except Exception as e:
        logger.error(f"❌ Training step test failed: {e}")
        traceback.print_exc()
        return False


def main():
    """Run all diagnostic tests."""
    logger.info("Starting CUDA diagnostic tests...")

    tests = [
        ("Basic CUDA", test_cuda_basic),
        ("CUDA Operations", test_cuda_operations),
        ("Memory Allocation", test_memory_allocation),
        ("Model Creation", test_model_creation),
        ("Training Step", test_training_step),
    ]

    results = []

    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info('='*50)

        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            logger.error(f"Test {test_name} crashed: {e}")
            results.append((test_name, False))

    # Print summary
    logger.info(f"\n{'='*50}")
    logger.info("DIAGNOSTIC SUMMARY")
    logger.info('='*50)

    passed = 0
    for test_name, success in results:
        status = "PASS" if success else "FAIL"
        logger.info(f"{test_name}: {status}")
        if success:
            passed += 1

    logger.info(f"\nOverall: {passed}/{len(results)} tests passed")

    # Recommendations
    logger.info(f"\n{'='*50}")
    logger.info("RECOMMENDATIONS")
    logger.info('='*50)

    if passed == len(results):
        logger.info("✅ All tests passed! Your system should work fine.")
        logger.info("Try running the training script again.")
    elif passed >= 3:
        logger.info("⚠️  Most tests passed. Try these solutions:")
        logger.info("1. Use smaller batch size: --batch-size 1")
        logger.info("2. Use CPU training: python scripts/train_cpu_safe.py --force-cpu")
        logger.info("3. Reduce model size in config")
    else:
        logger.info("❌ Multiple tests failed. Recommendations:")
        logger.info("1. Update CUDA drivers")
        logger.info("2. Reinstall PyTorch with correct CUDA version")
        logger.info("3. Use CPU training: python scripts/train_cpu_safe.py --force-cpu")
        logger.info("4. Check GPU memory usage by other processes")

    return passed == len(results)


if __name__ == "__main__":
    main()
