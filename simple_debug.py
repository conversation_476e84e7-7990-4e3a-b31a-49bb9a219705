"""
Simple debug without transformers dependency.
"""

import torch
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_simple_loss():
    """Test simple CrossEntropyLoss with various inputs."""
    logger.info("Testing simple CrossEntropyLoss...")
    
    vocab_size = 1000
    batch_size = 2
    seq_len = 5
    
    # Create loss function
    loss_fn = torch.nn.CrossEntropyLoss(ignore_index=-100)
    
    # Test 1: Normal case
    try:
        logits = torch.randn(batch_size * seq_len, vocab_size)
        labels = torch.randint(0, vocab_size, (batch_size * seq_len,))
        
        loss = loss_fn(logits, labels)
        logger.info(f"✅ Normal case: loss = {loss.item()}")
    except Exception as e:
        logger.error(f"❌ Normal case failed: {e}")
        return False
    
    # Test 2: With ignore tokens
    try:
        labels_with_ignore = labels.clone()
        labels_with_ignore[0] = -100  # Ignore first token
        
        loss = loss_fn(logits, labels_with_ignore)
        logger.info(f"✅ With ignore tokens: loss = {loss.item()}")
    except Exception as e:
        logger.error(f"❌ With ignore tokens failed: {e}")
        return False
    
    # Test 3: Edge case - all ignored
    try:
        all_ignore = torch.full((batch_size * seq_len,), -100)
        loss = loss_fn(logits, all_ignore)
        logger.info(f"✅ All ignored: loss = {loss.item()}")
    except Exception as e:
        logger.error(f"❌ All ignored failed: {e}")
        return False
    
    # Test 4: Out of bounds labels (this should fail)
    try:
        bad_labels = torch.tensor([vocab_size, vocab_size + 1])  # Out of bounds
        bad_logits = torch.randn(2, vocab_size)
        loss = loss_fn(bad_logits, bad_labels)
        logger.error(f"❌ Out of bounds should have failed but didn't: loss = {loss.item()}")
        return False
    except Exception as e:
        logger.info(f"✅ Out of bounds correctly failed: {e}")
    
    return True


def test_model_components():
    """Test individual model components."""
    logger.info("Testing model components...")
    
    try:
        from thinker_llm.models import IntegratedThinkerLLM
        from thinker_llm.utils.config import ModelConfig
        
        # Very small model
        config = ModelConfig(
            vocab_size=100,  # Very small vocab
            hidden_size=32,
            num_layers=1,
            num_attention_heads=2,
            thinker_hidden_size=48,
            thinker_num_layers=1,
            use_flash_attention=False,
            gradient_checkpointing=False,
        )
        
        model = IntegratedThinkerLLM(config)
        logger.info("✅ Model created")
        
        # Test forward pass
        batch_size = 1
        seq_len = 3
        input_ids = torch.randint(0, config.vocab_size, (batch_size, seq_len))
        
        logger.info(f"Input IDs: {input_ids}")
        logger.info(f"Input range: {input_ids.min().item()} to {input_ids.max().item()}")
        
        with torch.no_grad():
            outputs = model(input_ids=input_ids)
        
        logger.info(f"✅ Forward pass successful")
        logger.info(f"Output logits shape: {outputs['logits'].shape}")
        
        return True
    except Exception as e:
        logger.error(f"❌ Model components failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_loss_with_model():
    """Test loss computation with model outputs."""
    logger.info("Testing loss with model outputs...")
    
    try:
        from thinker_llm.models import IntegratedThinkerLLM
        from thinker_llm.utils.config import ModelConfig
        from thinker_llm.training.losses import CombinedLoss
        
        # Very small model
        config = ModelConfig(
            vocab_size=100,
            hidden_size=32,
            num_layers=1,
            num_attention_heads=2,
            thinker_hidden_size=48,
            thinker_num_layers=1,
            use_flash_attention=False,
            gradient_checkpointing=False,
        )
        
        model = IntegratedThinkerLLM(config)
        loss_fn = CombinedLoss(vocab_size=config.vocab_size)
        
        # Create inputs
        batch_size = 1
        seq_len = 3
        input_ids = torch.randint(0, config.vocab_size, (batch_size, seq_len))
        labels = torch.randint(0, config.vocab_size, (batch_size, seq_len))
        
        # Mask first token in labels (common practice)
        labels[0, 0] = -100
        
        logger.info(f"Input IDs: {input_ids}")
        logger.info(f"Labels: {labels}")
        logger.info(f"Labels range: {labels[labels != -100].min().item()} to {labels[labels != -100].max().item()}")
        
        # Forward pass
        outputs = model(input_ids=input_ids, labels=labels, return_thoughts=True)
        logger.info("✅ Model forward pass successful")
        
        # Loss computation
        decision_labels = torch.tensor([0.0])  # No thinking required
        
        losses = loss_fn(
            model_outputs=outputs,
            labels=labels,
            decision_labels=decision_labels,
        )
        
        logger.info(f"✅ Loss computation successful")
        logger.info(f"Total loss: {losses['total_loss'].item()}")
        
        # Test backward pass
        losses['total_loss'].backward()
        logger.info("✅ Backward pass successful")
        
        return True
    except Exception as e:
        logger.error(f"❌ Loss with model failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run simple debug tests."""
    logger.info("Starting simple debug tests...")
    
    tests = [
        ("Simple Loss", test_simple_loss),
        ("Model Components", test_model_components),
        ("Loss with Model", test_loss_with_model),
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*40}")
        logger.info(f"Running: {test_name}")
        logger.info('='*40)
        
        try:
            success = test_func()
            if success:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
                break
        except Exception as e:
            logger.error(f"❌ {test_name}: CRASHED - {e}")
            import traceback
            traceback.print_exc()
            break
    
    logger.info("Simple debug tests completed")


if __name__ == "__main__":
    main()
