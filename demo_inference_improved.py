#!/usr/bin/env python3
"""
Improved Demo script for ThinkerLLM inference capabilities with fallback support
Shows various examples of the model's thinking process with robust error handling
"""

import sys
import os
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent))

import torch
import json
from transformers import AutoTokenizer
import logging

# Import ThinkerLLM components
from thinker_llm.models import IntegratedThinkerLL<PERSON>
from thinker_llm.utils.config import ModelConfig
from fallback_generator import ImprovedThinkerGenerator

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImprovedThinkerDemo:
    """Improved demo class for showcasing ThinkerLLM capabilities with fallback support."""
    
    def __init__(self, model_path=None):
        """Initialize the demo with model and tokenizer."""
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.load_model(model_path)
    
    def load_model(self, model_path=None):
        """Load model and tokenizer."""
        try:
            logger.info("Loading ThinkerLLM for improved demo...")
            # Setup tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained("gpt2")
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Add special tokens
            special_tokens = ["<|thinking|>", "<|/thinking|>", "<|response|>", "<|/response|>"]
            new_tokens = [token for token in special_tokens if token not in self.tokenizer.get_vocab()]
            if new_tokens:
                self.tokenizer.add_tokens(new_tokens)
                
            checkpoint = {}
            # Load trained weights if available
            if model_path and os.path.exists(model_path):
                logger.info(f"Loading trained model from {model_path}")
                checkpoint = torch.load(model_path, map_location=self.device)                
                logger.info("✅ Trained model loaded successfully")
            else:
                logger.info("⚠️  Using base model (not trained)")
            
            # Create model
            if "model_config" in checkpoint:
                model_config = ModelConfig(**checkpoint['model_config'])
            else:
                model_config = ModelConfig()
            model_config.vocab_size = len(self.tokenizer)
            model_config.use_flash_attention = False
            
            self.model = IntegratedThinkerLLM(model_config)
            self.model.resize_token_embeddings(len(self.tokenizer))
            
            if 'model_state_dict' in checkpoint:
                self.model.load_state_dict(checkpoint['model_state_dict'])
            elif checkpoint:
                self.model.load_state_dict(checkpoint)
            
            self.model.to(self.device)
            self.model.eval()
            
            # Create improved generator with fallback capabilities
            self.generator = ImprovedThinkerGenerator(self.model, self.tokenizer)
            
            logger.info(f"Improved demo initialized on {self.device}")
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise
    
    def run_demo_examples(self):
        """Run a series of demo examples."""
        
        examples = [
            {
                "name": "Mathematical Reasoning",
                "prompt": "What is 15 * 23? Show your work step by step.",
                "params": {"temperature": 0.3, "thinking_budget": 0.4}
            },
            {
                "name": "Speed Calculation",
                "prompt": "If a train travels 120 miles in 2 hours, what is its average speed?",
                "params": {"temperature": 0.3, "thinking_budget": 0.4}
            },
            {
                "name": "Creative Writing",
                "prompt": "Write a short story about a robot learning to paint.",
                "params": {"temperature": 0.8, "thinking_budget": 0.3}
            },
            {
                "name": "Problem Solving",
                "prompt": "How would you design a system to reduce traffic congestion in a city?",
                "params": {"temperature": 0.6, "thinking_budget": 0.5}
            },
            {
                "name": "Logical Puzzle",
                "prompt": "If all roses are flowers, and some flowers are red, can we conclude that some roses are red?",
                "params": {"temperature": 0.2, "thinking_budget": 0.6}
            }
        ]
        
        print("🧠 ThinkerLLM Improved Demo - Showcasing Thinking Process with Fallback Support")
        print("=" * 80)
        
        for i, example in enumerate(examples, 1):
            print(f"\n📝 Example {i}: {example['name']}")
            print("-" * 50)
            print(f"Prompt: {example['prompt']}")
            print()
            
            # Generate response
            result = self.generator.generate(
                prompt=example['prompt'],
                max_length=400,
                return_thoughts=True,
                debug=True,
                **example['params']
            )
            
            # Display generation method used
            method = result.get('generation_method', 'unknown')
            fallback_used = result.get('fallback_used', False)
            
            if fallback_used:
                print(f"🔄 Generation Method: {method} (fallback)")
            else:
                print(f"🔄 Generation Method: {method}")
            
            # Display thinking process
            thinking = result.get('thinking', '').strip()
            if thinking:
                print("🤔 Thinking Process:")
                print(f"   {thinking}")
                print()
            
            # Display response
            response = result.get('response', '').strip()
            print("💡 Response:")
            if response:
                print(f"   {response}")
            else:
                print("   [No response generated]")
            print()
            
            # Display stats
            stats = result.get('token_stats', {})
            print("📊 Generation Stats:")
            print(f"   Thinking tokens: {stats.get('thinking_tokens', 0)}")
            print(f"   Response tokens: {stats.get('response_tokens', 0)}")
            print(f"   Total tokens: {stats.get('total_tokens', 0)}")
            
            # Display debug info if available
            if 'debug_info' in result:
                print("🔍 Debug Info:")
                debug_info = result['debug_info']
                for key, value in debug_info.items():
                    print(f"   {key}: {value}")
            
            print("\n" + "=" * 80)
            
            # Pause between examples
            input("Press Enter to continue to next example...")
    
    def interactive_mode(self):
        """Run interactive demo mode."""
        print("\n🎮 Improved Interactive Mode")
        print("Type your prompts and see ThinkerLLM's thinking process with fallback support!")
        print("Commands: 'quit' to exit, 'params' to adjust parameters")
        print("-" * 70)
        
        # Default parameters
        params = {
            "temperature": 0.7,
            "top_p": 0.9,
            "thinking_budget": 0.3,
            "max_length": 512
        }
        
        while True:
            try:
                prompt = input("\n💭 Your prompt: ").strip()
                
                if prompt.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                
                if prompt.lower() == 'params':
                    print("\n⚙️ Current parameters:")
                    for key, value in params.items():
                        print(f"   {key}: {value}")
                    
                    print("\nEnter new values (press Enter to keep current):")
                    for key in params:
                        new_value = input(f"   {key} [{params[key]}]: ").strip()
                        if new_value:
                            try:
                                params[key] = float(new_value) if '.' in new_value else int(new_value)
                            except ValueError:
                                print(f"   Invalid value for {key}, keeping {params[key]}")
                    continue
                
                if not prompt:
                    continue
                
                print("\n🔄 Generating response...")
                
                # Generate response
                result = self.generator.generate(
                    prompt=prompt,
                    return_thoughts=True,
                    debug=True,
                    **params
                )
                
                # Display generation method used
                method = result.get('generation_method', 'unknown')
                fallback_used = result.get('fallback_used', False)
                
                if fallback_used:
                    print(f"\n🔄 Method: {method} (fallback used)")
                else:
                    print(f"\n🔄 Method: {method}")
                
                # Display results
                thinking = result.get('thinking', '').strip()
                response = result.get('response', '').strip()
                
                if thinking:
                    print(f"\n🤔 Thinking: {thinking}")
                
                if response:
                    print(f"\n💡 Response: {response}")
                else:
                    print(f"\n💡 Response: [No meaningful response generated]")
                
                # Show stats
                stats = result.get('token_stats', {})
                print(f"\n📊 Stats: {stats.get('thinking_tokens', 0)} thinking + {stats.get('response_tokens', 0)} response tokens")
                
                # Show debug info if available
                if 'debug_info' in result:
                    print("\n🔍 Debug Info:")
                    for key, value in result['debug_info'].items():
                        print(f"   {key}: {value}")
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"\n❌ Error: {e}")

def main():
    """Main demo function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="ThinkerLLM Improved Demo")
    parser.add_argument("--model-path", type=str, help="Path to trained model checkpoint")
    parser.add_argument("--mode", choices=["examples", "interactive", "both"], 
                       default="both", help="Demo mode to run")
    
    args = parser.parse_args()
    
    # Initialize demo
    demo = ImprovedThinkerDemo(model_path=args.model_path)
    
    # Run demo based on mode
    if args.mode in ["examples", "both"]:
        demo.run_demo_examples()
    
    if args.mode in ["interactive", "both"]:
        demo.interactive_mode()

if __name__ == "__main__":
    main()
