"""
Projection layers for connecting ThinkerModule outputs to MainLLM.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple
from .attention import CrossAttention


class ProjectionLayer(nn.Module):
    """
    Base projection layer for converting ThinkerModule hidden states
    to be compatible with MainLLM.
    """
    
    def __init__(
        self,
        input_dim: int,
        output_dim: int,
        hidden_dim: Optional[int] = None,
        dropout: float = 0.1,
    ):
        super().__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.hidden_dim = hidden_dim or (input_dim + output_dim) // 2
        self.dropout = dropout
        
        self.projection = nn.Sequential(
            nn.Linear(input_dim, self.hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(self.hidden_dim, output_dim),
            nn.LayerNorm(output_dim),
        )
        
    def forward(self, hidden_states: torch.Tensor) -> torch.Tensor:
        """
        Project ThinkerModule hidden states to MainLLM dimension.
        
        Args:
            hidden_states: Tensor of shape (batch_size, seq_len, input_dim)
            
        Returns:
            Projected tensor of shape (batch_size, seq_len, output_dim)
        """
        return self.projection(hidden_states)


class AdaptiveProjection(nn.Module):
    """
    Adaptive projection layer that learns to selectively project
    different aspects of ThinkerModule outputs.
    """
    
    def __init__(
        self,
        input_dim: int,
        output_dim: int,
        num_experts: int = 4,
        hidden_dim: Optional[int] = None,
        dropout: float = 0.1,
    ):
        super().__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.num_experts = num_experts
        self.hidden_dim = hidden_dim or (input_dim + output_dim) // 2
        self.dropout = dropout
        
        # Gating network to select experts
        self.gate = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, num_experts),
            nn.Softmax(dim=-1),
        )
        
        # Expert projection networks
        self.experts = nn.ModuleList([
            nn.Sequential(
                nn.Linear(input_dim, self.hidden_dim),
                nn.GELU(),
                nn.Dropout(dropout),
                nn.Linear(self.hidden_dim, output_dim),
            )
            for _ in range(num_experts)
        ])
        
        self.layer_norm = nn.LayerNorm(output_dim)
        
    def forward(self, hidden_states: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Adaptively project hidden states using mixture of experts.
        
        Args:
            hidden_states: Tensor of shape (batch_size, seq_len, input_dim)
            
        Returns:
            Tuple of (projected_states, gate_weights)
        """
        batch_size, seq_len, _ = hidden_states.shape
        
        # Compute gating weights
        gate_weights = self.gate(hidden_states)  # (batch_size, seq_len, num_experts)
        
        # Apply experts
        expert_outputs = []
        for expert in self.experts:
            expert_output = expert(hidden_states)  # (batch_size, seq_len, output_dim)
            expert_outputs.append(expert_output)
        
        # Stack expert outputs
        expert_outputs = torch.stack(expert_outputs, dim=-1)  # (batch_size, seq_len, output_dim, num_experts)
        
        # Weighted combination of expert outputs
        gate_weights = gate_weights.unsqueeze(-2)  # (batch_size, seq_len, 1, num_experts)
        projected_states = torch.sum(expert_outputs * gate_weights, dim=-1)  # (batch_size, seq_len, output_dim)
        
        # Apply layer normalization
        projected_states = self.layer_norm(projected_states)
        
        return projected_states, gate_weights.squeeze(-2)


class CrossAttentionProjection(nn.Module):
    """
    Projection layer using cross-attention mechanism to integrate
    ThinkerModule outputs with MainLLM states.
    """
    
    def __init__(
        self,
        llm_dim: int,
        thinker_dim: int,
        num_heads: int = 8,
        hidden_dim: Optional[int] = None,
        dropout: float = 0.1,
    ):
        super().__init__()
        self.llm_dim = llm_dim
        self.thinker_dim = thinker_dim
        self.num_heads = num_heads
        self.hidden_dim = hidden_dim or llm_dim
        self.dropout = dropout
        
        # Cross-attention from LLM to Thinker
        self.cross_attention = CrossAttention(
            query_dim=llm_dim,
            key_value_dim=thinker_dim,
            num_heads=num_heads,
            dropout=dropout,
        )
        
        # Feed-forward network for final projection
        self.ffn = nn.Sequential(
            nn.Linear(llm_dim, self.hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(self.hidden_dim, llm_dim),
        )
        
        self.layer_norm1 = nn.LayerNorm(llm_dim)
        self.layer_norm2 = nn.LayerNorm(llm_dim)
        
    def forward(
        self,
        llm_states: torch.Tensor,
        thinker_states: torch.Tensor,
        thinker_mask: Optional[torch.Tensor] = None,
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Project using cross-attention between LLM and Thinker states.
        
        Args:
            llm_states: MainLLM hidden states (batch_size, llm_seq_len, llm_dim)
            thinker_states: ThinkerModule states (batch_size, thinker_seq_len, thinker_dim)
            thinker_mask: Optional mask for thinker states
            
        Returns:
            Tuple of (enhanced_llm_states, attention_weights)
        """
        # Cross-attention
        attn_output, attn_weights = self.cross_attention(
            query_states=llm_states,
            key_value_states=thinker_states,
            attention_mask=thinker_mask,
        )
        
        # Residual connection and layer norm
        llm_states = self.layer_norm1(llm_states + attn_output)
        
        # Feed-forward network
        ffn_output = self.ffn(llm_states)
        
        # Residual connection and layer norm
        enhanced_states = self.layer_norm2(llm_states + ffn_output)
        
        return enhanced_states, attn_weights


class ContextualProjection(nn.Module):
    """
    Context-aware projection that adapts based on input characteristics.
    """
    
    def __init__(
        self,
        input_dim: int,
        output_dim: int,
        context_dim: int,
        hidden_dim: Optional[int] = None,
        dropout: float = 0.1,
    ):
        super().__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.context_dim = context_dim
        self.hidden_dim = hidden_dim or (input_dim + output_dim) // 2
        self.dropout = dropout
        
        # Context encoder
        self.context_encoder = nn.Sequential(
            nn.Linear(context_dim, self.hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(self.hidden_dim, self.hidden_dim),
        )
        
        # Dynamic projection weights generator
        self.weight_generator = nn.Sequential(
            nn.Linear(self.hidden_dim, input_dim * output_dim),
            nn.Tanh(),
        )
        
        # Bias generator
        self.bias_generator = nn.Sequential(
            nn.Linear(self.hidden_dim, output_dim),
        )
        
        self.layer_norm = nn.LayerNorm(output_dim)
        
    def forward(
        self,
        hidden_states: torch.Tensor,
        context: torch.Tensor,
    ) -> torch.Tensor:
        """
        Context-aware projection of hidden states.
        
        Args:
            hidden_states: Input states (batch_size, seq_len, input_dim)
            context: Context information (batch_size, context_dim)
            
        Returns:
            Projected states (batch_size, seq_len, output_dim)
        """
        batch_size, seq_len, _ = hidden_states.shape
        
        # Encode context
        context_encoded = self.context_encoder(context)  # (batch_size, hidden_dim)
        
        # Generate dynamic weights and biases
        weights = self.weight_generator(context_encoded)  # (batch_size, input_dim * output_dim)
        weights = weights.view(batch_size, self.input_dim, self.output_dim)
        
        biases = self.bias_generator(context_encoded)  # (batch_size, output_dim)
        
        # Apply dynamic projection
        # hidden_states: (batch_size, seq_len, input_dim)
        # weights: (batch_size, input_dim, output_dim)
        projected = torch.bmm(
            hidden_states.view(batch_size * seq_len, 1, self.input_dim),
            weights.unsqueeze(1).expand(-1, seq_len, -1, -1).contiguous().view(
                batch_size * seq_len, self.input_dim, self.output_dim
            )
        ).squeeze(1)  # (batch_size * seq_len, output_dim)
        
        projected = projected.view(batch_size, seq_len, self.output_dim)
        
        # Add bias
        projected = projected + biases.unsqueeze(1)
        
        # Apply layer normalization
        projected = self.layer_norm(projected)
        
        return projected


class MultiScaleProjection(nn.Module):
    """
    Multi-scale projection that captures different temporal scales
    of ThinkerModule outputs.
    """
    
    def __init__(
        self,
        input_dim: int,
        output_dim: int,
        scales: list = [1, 2, 4, 8],
        hidden_dim: Optional[int] = None,
        dropout: float = 0.1,
    ):
        super().__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.scales = scales
        self.hidden_dim = hidden_dim or (input_dim + output_dim) // 2
        self.dropout = dropout
        
        # Multi-scale convolutions
        self.scale_convs = nn.ModuleList([
            nn.Conv1d(
                input_dim, 
                self.hidden_dim // len(scales),
                kernel_size=scale,
                padding=scale // 2,
                groups=1,
            )
            for scale in scales
        ])
        
        # Final projection
        self.final_projection = nn.Sequential(
            nn.Linear(self.hidden_dim, output_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(output_dim, output_dim),
        )
        
        self.layer_norm = nn.LayerNorm(output_dim)
        
    def forward(self, hidden_states: torch.Tensor) -> torch.Tensor:
        """
        Multi-scale projection of hidden states.
        
        Args:
            hidden_states: Input states (batch_size, seq_len, input_dim)
            
        Returns:
            Projected states (batch_size, seq_len, output_dim)
        """
        batch_size, seq_len, _ = hidden_states.shape
        
        # Transpose for convolution: (batch_size, input_dim, seq_len)
        conv_input = hidden_states.transpose(1, 2)
        
        # Apply multi-scale convolutions
        scale_outputs = []
        for conv in self.scale_convs:
            scale_output = conv(conv_input)  # (batch_size, hidden_dim//len(scales), seq_len)
            scale_outputs.append(scale_output)
        
        # Concatenate scale outputs
        multi_scale_features = torch.cat(scale_outputs, dim=1)  # (batch_size, hidden_dim, seq_len)
        
        # Transpose back: (batch_size, seq_len, hidden_dim)
        multi_scale_features = multi_scale_features.transpose(1, 2)
        
        # Final projection
        projected = self.final_projection(multi_scale_features)
        projected = self.layer_norm(projected)
        
        return projected
