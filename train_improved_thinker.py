#!/usr/bin/env python3
"""
Improved training script for ThinkerLLM that addresses the critical issues:
1. Uses proper training data
2. Better training configuration
3. Proper model diagnostics
4. Better learning rate and epochs
"""

import sys
import os
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent))

import torch
import logging
from transformers import AutoTokenizer
import argparse

# Import ThinkerLLM components
from thinker_llm.models import IntegratedThinkerLLM
from thinker_llm.utils.config import ModelConfig, TrainingConfig
from thinker_llm.training.trainer import ThinkerLLMTrainer
from thinker_llm.training.data_loader import ThinkerDataset, ThinkerDataLoader

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def setup_tokenizer():
    """Setup tokenizer with special tokens."""
    logger.info("Setting up tokenizer...")

    tokenizer = AutoTokenizer.from_pretrained("gpt2")
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    # Add special tokens
    special_tokens = ["<|thinking|>", "<|/thinking|>", "<|response|>", "<|/response|>"]
    new_tokens = [token for token in special_tokens if token not in tokenizer.get_vocab()]

    if new_tokens:
        num_added = tokenizer.add_tokens(new_tokens)
        logger.info(f"Added {num_added} special tokens to tokenizer")

    logger.info(f"Final tokenizer vocab size: {len(tokenizer)}")

    # Verify special tokens
    for token in special_tokens:
        token_id = tokenizer.convert_tokens_to_ids(token)
        logger.info(f"Token '{token}' -> ID {token_id}")

    return tokenizer


def create_improved_configs():
    """Create improved model and training configurations."""

    # Model configuration - smaller for faster training but still capable
    model_config = ModelConfig(
        vocab_size=50261,  # Will be updated after tokenizer setup
        hidden_size=384,   # Smaller for faster training
        num_layers=6,      # Reasonable depth
        num_attention_heads=6,  # Correct parameter name
        intermediate_size=1536,
        max_position_embeddings=1024,
        dropout_prob=0.1,  # Correct parameter name
        layer_norm_eps=1e-5,
        use_flash_attention=False,  # Disable for compatibility

        # ThinkerModule configuration
        thinker_hidden_size=512,
        thinker_num_layers=4,
        thinker_num_heads=8,
        thinker_intermediate_size=2048,
        thinker_max_length=256,
        thinker_dropout=0.1,

        # Decision mechanism configuration
        decision_hidden_size=256,
        decision_num_layers=3,
        decision_threshold=0.5,
        decision_features=["input_length", "complexity_score", "question_type"],

        # Projection configuration
        projection_type="adaptive",
        projection_hidden_size=256,
        projection_num_layers=2,

        # Other settings
        gradient_checkpointing=False,
        tie_word_embeddings=True,
    )

    # Training configuration - much better than original
    training_config = TrainingConfig(
        # Basic training params
        batch_size=4,  # Larger batch size for better gradients
        learning_rate=2e-5,  # Lower learning rate for stable training
        num_epochs=2,  # More epochs for proper learning
        warmup_steps=100,  # More warmup for stability

        # Optimization
        max_grad_norm=1.0,
        weight_decay=0.01,
        gradient_accumulation_steps=4,  # Effective batch size = 4 * 4 = 16

        # Loss weights
        llm_loss_weight=1.0,
        thinker_loss_weight=0.8,  # Slightly higher for thinking
        decision_loss_weight=0.6,

        # Training strategy
        joint_training=True,
        pretrain_components=False,
        freeze_llm=False,
        freeze_thinker=False,

        # Optimizer and scheduler
        optimizer="adamw",
        scheduler="cosine",

        # Logging and saving
        logging_steps=25,  # More frequent logging
        eval_steps=100,
        save_steps=200,
        save_total_limit=5,

        # Data params
        max_length=512,
        thinking_ratio=0.8,  # High ratio since most samples require thinking

        # Output
        output_dir="./outputs/improved_training",
        logging_dir="./outputs/improved_training/logs",
        cache_dir="./cache"
    )

    return model_config, training_config


def prepare_datasets(tokenizer, training_config):
    """Prepare training and validation datasets."""
    logger.info("Preparing datasets...")

    # Training dataset
    train_dataset = ThinkerDataset(
        data_path="data/train_cleaned.jsonl",
        tokenizer=tokenizer,
        max_length=training_config.max_length,
        thinking_ratio=training_config.thinking_ratio,
        split="train",
    )

    train_dataloader = ThinkerDataLoader(
        dataset=train_dataset,
        batch_size=training_config.batch_size,
        shuffle=True,
    )

    # Validation dataset
    val_dataset = ThinkerDataset(
        data_path="data/val_improved.jsonl",
        tokenizer=tokenizer,
        max_length=training_config.max_length,
        thinking_ratio=training_config.thinking_ratio,
        split="train",  # Use "train" since our data doesn't have explicit splits
    )

    val_dataloader = ThinkerDataLoader(
        dataset=val_dataset,
        batch_size=training_config.batch_size,
        shuffle=False,
    )

    logger.info(f"Training dataset: {len(train_dataset)} samples")
    logger.info(f"Validation dataset: {len(val_dataset)} samples")

    return train_dataloader, val_dataloader


def create_model(model_config, tokenizer):
    """Create and initialize the model."""
    logger.info("Creating model...")

    # Update vocab size to match tokenizer
    model_config.vocab_size = len(tokenizer)

    # Create model
    model = IntegratedThinkerLLM(model_config)

    # Resize token embeddings to match tokenizer
    model.resize_token_embeddings(len(tokenizer))

    # Set training mode
    model.set_training_mode("joint")

    logger.info(f"Model created with vocab size: {model_config.vocab_size}")
    logger.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    logger.info(f"Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

    return model


def run_training_diagnostics(model, train_dataloader, tokenizer):
    """Run diagnostics to check if training setup is correct."""
    logger.info("Running training diagnostics...")

    # Get a sample batch
    sample_batch = next(iter(train_dataloader.get_dataloader()))

    # Check batch structure
    logger.info(f"Batch keys: {sample_batch.keys()}")
    logger.info(f"Input IDs shape: {sample_batch['input_ids'].shape}")
    logger.info(f"Labels shape: {sample_batch['labels'].shape}")

    # Check for special tokens in the batch
    input_ids = sample_batch['input_ids'][0]  # First sample
    special_token_ids = [
        tokenizer.convert_tokens_to_ids("<|thinking|>"),
        tokenizer.convert_tokens_to_ids("<|/thinking|>"),
        tokenizer.convert_tokens_to_ids("<|response|>"),
        tokenizer.convert_tokens_to_ids("<|/response|>")
    ]

    for token_id in special_token_ids:
        if token_id in input_ids:
            token = tokenizer.convert_ids_to_tokens([token_id])[0]
            logger.info(f"Found special token '{token}' (ID: {token_id}) in batch")

    # Test forward pass
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.to(device)
    model.eval()

    with torch.no_grad():
        # Prepare model inputs (only the parameters the model expects)
        model_inputs = {
            "input_ids": sample_batch["input_ids"].to(device),
            "attention_mask": sample_batch["attention_mask"].to(device),
            "labels": sample_batch["labels"].to(device),
            "return_thoughts": True,
        }

        try:
            outputs = model(**model_inputs)
            logger.info("✅ Forward pass successful")
            logger.info(f"Loss: {outputs['loss'].item():.4f}")

            # Check output structure
            logger.info(f"Output keys: {list(outputs.keys())}")
            logger.info(f"Logits shape: {outputs['logits'].shape}")

            # Check decision mechanism
            if 'decision_info' in outputs:
                decision_info = outputs['decision_info']
                logger.info(f"Decision info keys: {list(decision_info.keys())}")

            if 'should_think_mask' in outputs:
                should_think = outputs['should_think_mask']
                logger.info(f"Should think mask: {should_think.sum().item()}/{should_think.size(0)} samples need thinking")

        except Exception as e:
            logger.error(f"❌ Forward pass failed: {e}")
            raise

    logger.info("Diagnostics completed successfully!")


def main():
    """Main training function."""
    parser = argparse.ArgumentParser(description="Improved ThinkerLLM Training")
    parser.add_argument("--dry-run", action="store_true", help="Run diagnostics only")
    parser.add_argument("--resume", type=str, help="Resume from checkpoint")

    args = parser.parse_args()

    logger.info("Starting improved ThinkerLLM training...")

    # Setup
    tokenizer = setup_tokenizer()
    model_config, training_config = create_improved_configs()

    # Create datasets
    train_dataloader, val_dataloader = prepare_datasets(tokenizer, training_config)

    # Create model
    model = create_model(model_config, tokenizer)

    # Run diagnostics
    run_training_diagnostics(model, train_dataloader, tokenizer)

    if args.dry_run:
        logger.info("Dry run completed successfully!")
        return

    # Create trainer
    logger.info("Setting up trainer...")
    trainer = ThinkerLLMTrainer(
        model=model,
        train_dataloader=train_dataloader.get_dataloader(),
        val_dataloader=val_dataloader.get_dataloader(),
        config=training_config,
        model_config=model_config,
    )

    # Resume from checkpoint if specified
    if args.resume:
        logger.info(f"Resuming from checkpoint: {args.resume}")
        trainer.load_checkpoint(args.resume)

    # Start training
    logger.info("Starting training...")
    trainer.train()

    logger.info("Training completed successfully!")


if __name__ == "__main__":
    main()
