{"time":"2025-05-29T23:09:31.4328944+07:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"outputs\\logs\\wandb\\run-20250529_230931-g04pt9i8\\logs\\debug-core.log"}
{"time":"2025-05-29T23:09:31.5428071+07:00","level":"INFO","msg":"created new stream","id":"g04pt9i8"}
{"time":"2025-05-29T23:09:31.5428071+07:00","level":"INFO","msg":"stream: started","id":"g04pt9i8"}
{"time":"2025-05-29T23:09:31.5428071+07:00","level":"INFO","msg":"sender: started","stream_id":"g04pt9i8"}
{"time":"2025-05-29T23:09:31.5428071+07:00","level":"INFO","msg":"writer: Do: started","stream_id":"g04pt9i8"}
{"time":"2025-05-29T23:09:31.5428071+07:00","level":"INFO","msg":"handler: started","stream_id":"g04pt9i8"}
{"time":"2025-05-29T23:09:31.9632625+07:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-05-29T23:09:46.9969161+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:10:01.9804539+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:10:16.9803094+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:10:17.0794745+07:00","level":"INFO","msg":"stream: closing","id":"g04pt9i8"}
{"time":"2025-05-29T23:10:17.0794745+07:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-05-29T23:10:17.0799831+07:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-05-29T23:10:18.6497675+07:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-05-29T23:10:18.9568708+07:00","level":"INFO","msg":"handler: closed","stream_id":"g04pt9i8"}
{"time":"2025-05-29T23:10:18.9568708+07:00","level":"INFO","msg":"sender: closed","stream_id":"g04pt9i8"}
{"time":"2025-05-29T23:10:18.9568708+07:00","level":"INFO","msg":"writer: Close: closed","stream_id":"g04pt9i8"}
{"time":"2025-05-29T23:10:18.9568708+07:00","level":"INFO","msg":"stream: closed","id":"g04pt9i8"}
