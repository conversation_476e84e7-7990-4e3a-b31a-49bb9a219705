#!/usr/bin/env python3
"""
Create proper training data for ThinkerLLM with diverse, high-quality examples.
This addresses the critical issue of insufficient training data.
"""

import json
import random
from typing import List, Dict, Any
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_math_problems() -> List[Dict[str, Any]]:
    """Create diverse math problems requiring step-by-step thinking."""
    problems = []

    # Basic arithmetic
    for _ in range(200):
        a, b = random.randint(10, 99), random.randint(10, 99)
        operation = random.choice(['+', '-', '*'])

        if operation == '+':
            result = a + b
            thinking = f"I need to add {a} and {b}.\n{a} + {b} = {result}"
            instruction = f"What is {a} + {b}?"
            output = f"{a} + {b} = {result}"
        elif operation == '-':
            if a < b:
                a, b = b, a  # Ensure positive result
            result = a - b
            thinking = f"I need to subtract {b} from {a}.\n{a} - {b} = {result}"
            instruction = f"What is {a} - {b}?"
            output = f"{a} - {b} = {result}"
        else:  # multiplication
            result = a * b
            thinking = f"I need to multiply {a} and {b}.\n{a} × {b} = {result}"
            instruction = f"What is {a} × {b}?"
            output = f"{a} × {b} = {result}"

        problems.append({
            "instruction": instruction,
            "thinking": thinking,
            "output": output,
            "requires_thinking": True
        })

    # Word problems
    word_problems = [
        {
            "instruction": "Sarah has 24 apples. She gives 8 to her friend and buys 15 more. How many apples does she have now?",
            "thinking": "Let me work through this step by step.\n\nSarah starts with: 24 apples\nShe gives away: 8 apples\nAfter giving away: 24 - 8 = 16 apples\nShe buys: 15 more apples\nFinal amount: 16 + 15 = 31 apples",
            "output": "Sarah has 31 apples. Starting with 24, after giving away 8 she has 16, then adding 15 more gives her 31 total.",
            "requires_thinking": True
        },
        {
            "instruction": "A train travels 180 miles in 3 hours. What is its average speed?",
            "thinking": "To find average speed, I need to divide distance by time.\n\nDistance = 180 miles\nTime = 3 hours\nSpeed = Distance ÷ Time\nSpeed = 180 ÷ 3 = 60 miles per hour",
            "output": "The train's average speed is 60 miles per hour. This is calculated by dividing the distance (180 miles) by the time (3 hours).",
            "requires_thinking": True
        },
        {
            "instruction": "If a rectangle has length 12 cm and width 8 cm, what is its area and perimeter?",
            "thinking": "I need to calculate both area and perimeter for this rectangle.\n\nGiven:\nLength = 12 cm\nWidth = 8 cm\n\nArea = length × width\nArea = 12 × 8 = 96 square cm\n\nPerimeter = 2 × (length + width)\nPerimeter = 2 × (12 + 8) = 2 × 20 = 40 cm",
            "output": "The rectangle has an area of 96 square cm and a perimeter of 40 cm.",
            "requires_thinking": True
        }
    ]

    problems.extend(word_problems)
    return problems


def create_reasoning_problems() -> List[Dict[str, Any]]:
    """Create logical reasoning problems."""
    problems = [
        {
            "instruction": "If all birds can fly, and penguins are birds, can penguins fly?",
            "thinking": "This is a logical reasoning problem. Let me analyze the premises:\n\nPremise 1: All birds can fly\nPremise 2: Penguins are birds\n\nIf I follow the logical structure:\n- All birds can fly (universal statement)\n- Penguins are birds (classification)\n- Therefore, penguins can fly (logical conclusion)\n\nHowever, this conflicts with real-world knowledge that penguins cannot fly. This shows the importance of checking premises against reality.",
            "output": "Based on the logical structure given, penguins would be able to fly. However, this conclusion reveals that the first premise 'all birds can fly' is actually false in reality, as penguins are indeed birds but cannot fly.",
            "requires_thinking": True
        },
        {
            "instruction": "You have a 3-gallon jug and a 5-gallon jug. How can you measure exactly 4 gallons?",
            "thinking": "This is a classic water jug problem. I need to find a sequence of operations to get exactly 4 gallons.\n\nLet me work through this step by step:\n1. Fill the 5-gallon jug completely (5 gallons)\n2. Pour from 5-gallon jug into 3-gallon jug (3-gallon jug now full, 5-gallon jug has 2 gallons left)\n3. Empty the 3-gallon jug\n4. Pour the 2 gallons from 5-gallon jug into the empty 3-gallon jug\n5. Fill the 5-gallon jug completely again\n6. Pour from 5-gallon jug into 3-gallon jug (which already has 2 gallons, so only 1 more gallon fits)\n7. The 5-gallon jug now has exactly 4 gallons remaining!",
            "output": "Fill the 5-gallon jug, pour 3 gallons into the 3-gallon jug (leaving 2 gallons). Empty the 3-gallon jug, pour the 2 gallons into it. Fill the 5-gallon jug again, then pour 1 gallon into the 3-gallon jug (which already has 2 gallons). This leaves exactly 4 gallons in the 5-gallon jug.",
            "requires_thinking": True
        }
    ]
    return problems


def create_creative_tasks() -> List[Dict[str, Any]]:
    """Create creative writing and generation tasks."""
    tasks = [
        {
            "instruction": "Write a short story about a robot learning to cook.",
            "thinking": "I need to create a short story with these elements:\n- A robot character\n- Learning process (cooking)\n- Character development\n- A narrative arc with beginning, middle, end\n\nI'll focus on the contrast between robotic precision and the art of cooking.",
            "output": "CHEF-9000 approached the kitchen with mechanical precision, sensors analyzing every ingredient. Its first attempt at pasta was perfectly measured but tasteless. Day by day, it watched human chefs, learning that cooking wasn't just about following recipes—it was about intuition, creativity, and love. The day CHEF-9000 added 'a pinch of this' and 'a dash of that' without measuring, creating a dish that made people smile, it finally understood that the secret ingredient was heart.",
            "requires_thinking": True
        },
        {
            "instruction": "Explain why someone might choose to walk to work instead of driving.",
            "thinking": "This question asks me to consider multiple factors that influence transportation choices. Let me think through various reasons:\n\n1. Health benefits - walking provides exercise and improves fitness\n2. Environmental impact - walking produces no emissions, reduces carbon footprint\n3. Cost considerations - no fuel, parking fees, or vehicle maintenance costs\n4. Time factors - might be faster in heavy traffic areas, no time spent finding parking\n5. Mental health - walking can reduce stress, provide thinking time\n6. Practical considerations - only viable for reasonable distances\n7. Urban planning - some areas are more walkable than others",
            "output": "People might choose walking over driving for several reasons: health benefits from regular exercise, environmental consciousness to reduce emissions, cost savings on fuel and parking, convenience in congested areas where walking might be faster, mental health benefits from fresh air and stress reduction, and the simple enjoyment of being outdoors. Walking is most practical for shorter distances in pedestrian-friendly areas.",
            "requires_thinking": True
        }
    ]
    return tasks


def create_factual_questions() -> List[Dict[str, Any]]:
    """Create factual questions that don't require complex thinking."""
    questions = []

    # Geography questions
    geo_questions = [
        ("What is the capital of France?", "The capital of France is Paris."),
        ("What is the capital of Japan?", "The capital of Japan is Tokyo."),
        ("What is the capital of Australia?", "The capital of Australia is Canberra."),
        ("What is the largest country by area?", "Russia is the largest country by area."),
        ("What is the longest river in the world?", "The Nile River is the longest river in the world."),
        ("What is the highest mountain in the world?", "Mount Everest is the highest mountain in the world."),
        ("What ocean is between Europe and America?", "The Atlantic Ocean is between Europe and America."),
        ("What continent is Egypt in?", "Egypt is in Africa."),
    ]

    # Science questions
    science_questions = [
        ("What color do you get when you mix red and blue?", "When you mix red and blue, you get purple."),
        ("How many days are in a week?", "There are 7 days in a week."),
        ("What is the largest planet in our solar system?", "Jupiter is the largest planet in our solar system."),
        ("What gas do plants absorb from the atmosphere during photosynthesis?", "Plants absorb carbon dioxide (CO2) from the atmosphere during photosynthesis."),
        ("What is the chemical symbol for water?", "The chemical symbol for water is H2O."),
        ("What is the speed of light?", "The speed of light is approximately 299,792,458 meters per second."),
        ("What is the smallest unit of matter?", "The atom is the smallest unit of matter."),
        ("What planet is closest to the sun?", "Mercury is the planet closest to the sun."),
    ]

    # History questions
    history_questions = [
        ("Who was the first president of the United States?", "George Washington was the first president of the United States."),
        ("In what year did World War II end?", "World War II ended in 1945."),
        ("Who painted the Mona Lisa?", "Leonardo da Vinci painted the Mona Lisa."),
        ("What year did the Berlin Wall fall?", "The Berlin Wall fell in 1989."),
        ("Who invented the telephone?", "Alexander Graham Bell invented the telephone."),
        ("What year did humans first land on the moon?", "Humans first landed on the moon in 1969."),
    ]

    # Convert to proper format
    for instruction, output in geo_questions + science_questions + history_questions:
        questions.append({
            "instruction": instruction,
            "thinking": "",
            "output": output,
            "requires_thinking": False
        })

    return questions


def create_explanation_tasks() -> List[Dict[str, Any]]:
    """Create tasks that require explanation and reasoning."""
    tasks = [
        {
            "instruction": "Explain how photosynthesis works.",
            "thinking": "I need to explain photosynthesis in a clear, step-by-step manner. Let me break down the key components:\n\n1. What it is - the process plants use to make food\n2. What's needed - sunlight, water, carbon dioxide\n3. Where it happens - mainly in leaves, in chloroplasts\n4. The chemical process - converting CO2 and water into glucose\n5. What's produced - glucose (food) and oxygen\n6. Why it's important - basis of most life on Earth",
            "output": "Photosynthesis is the process by which plants convert sunlight into food. Plants absorb carbon dioxide from the air through their leaves and water through their roots. Using chlorophyll (the green pigment in leaves), they capture sunlight energy to combine CO2 and water into glucose (sugar), which serves as food for the plant. Oxygen is released as a byproduct. This process is essential for life on Earth as it produces the oxygen we breathe and forms the base of most food chains.",
            "requires_thinking": True
        },
        {
            "instruction": "Why do seasons change?",
            "thinking": "I need to explain the cause of seasons. The key points are:\n\n1. Earth's tilt - the planet is tilted about 23.5 degrees\n2. Earth's orbit around the sun\n3. How tilt affects sunlight exposure\n4. Different hemispheres experience opposite seasons\n5. The solstices and equinoxes\n\nIt's important to clarify that it's NOT because Earth is closer or farther from the sun.",
            "output": "Seasons change because Earth is tilted on its axis by about 23.5 degrees as it orbits the sun. When the Northern Hemisphere is tilted toward the sun, it receives more direct sunlight and experiences summer, while the Southern Hemisphere is tilted away and experiences winter. Six months later, the situation reverses. The tilt means different parts of Earth receive varying amounts of sunlight throughout the year, creating the seasonal cycle. This is why opposite hemispheres have opposite seasons at the same time.",
            "requires_thinking": True
        }
    ]
    return tasks


def create_coding_problems() -> List[Dict[str, Any]]:
    """Create simple coding and algorithm problems."""
    problems = [
        {
            "instruction": "Write a function to check if a number is even or odd.",
            "thinking": "I need to create a function that determines if a number is even or odd.\n\nA number is even if it's divisible by 2 (remainder is 0 when divided by 2).\nA number is odd if it's not divisible by 2 (remainder is 1 when divided by 2).\n\nI can use the modulo operator (%) to check the remainder.",
            "output": "Here's a function to check if a number is even or odd:\n\n```python\ndef is_even_or_odd(number):\n    if number % 2 == 0:\n        return 'even'\n    else:\n        return 'odd'\n```\n\nThis function uses the modulo operator (%) to check if the remainder when dividing by 2 is 0 (even) or not (odd).",
            "requires_thinking": True
        },
        {
            "instruction": "How do you reverse a string?",
            "thinking": "There are several ways to reverse a string. Let me think through the most common approaches:\n\n1. Using slicing with [::-1] - most Pythonic\n2. Using a loop to build the string backwards\n3. Using built-in functions like reversed()\n\nI'll show the slicing method as it's the most efficient and readable.",
            "output": "To reverse a string in Python, you can use slicing:\n\n```python\noriginal = 'hello'\nreversed_string = original[::-1]\nprint(reversed_string)  # Output: 'olleh'\n```\n\nThe `[::-1]` slice notation means start from the end and go backwards with a step of -1.",
            "requires_thinking": True
        }
    ]
    return problems


def create_language_tasks() -> List[Dict[str, Any]]:
    """Create language and communication tasks."""
    tasks = [
        {
            "instruction": "Explain the difference between 'affect' and 'effect'.",
            "thinking": "This is a common grammar question. Let me think through the key differences:\n\n'Affect' is usually a verb meaning to influence or make a change\n'Effect' is usually a noun meaning a result or consequence\n\nMemory trick: Affect = Action (both start with A), Effect = End result (both start with E)\n\nI should provide clear examples to illustrate the difference.",
            "output": "'Affect' and 'effect' are commonly confused words:\n\n- **Affect** (verb): means to influence or cause a change\n  Example: 'The rain will affect our picnic plans.'\n\n- **Effect** (noun): means a result or consequence\n  Example: 'The effect of the rain was a cancelled picnic.'\n\nMemory tip: **A**ffect = **A**ction (verb), **E**ffect = **E**nd result (noun).",
            "requires_thinking": True
        },
        {
            "instruction": "What is a metaphor? Give an example.",
            "thinking": "A metaphor is a figure of speech that makes a comparison between two unlike things without using 'like' or 'as' (which would make it a simile).\n\nKey points:\n- Direct comparison\n- No 'like' or 'as'\n- Helps explain or describe something by comparing it to something else\n- Makes writing more vivid and interesting\n\nI should give a clear definition and a good example.",
            "output": "A metaphor is a figure of speech that directly compares two unlike things without using 'like' or 'as'. It states that one thing IS another thing to create a vivid image or explanation.\n\nExample: 'Time is money.'\nThis doesn't mean time literally is money, but it compares time to money to emphasize that time is valuable and shouldn't be wasted.",
            "requires_thinking": True
        }
    ]
    return tasks


def create_problem_solving_tasks() -> List[Dict[str, Any]]:
    """Create general problem-solving scenarios."""
    tasks = [
        {
            "instruction": "You're locked out of your house and your phone is dead. What would you do?",
            "thinking": "This is a practical problem-solving scenario. I need to think through various options systematically:\n\n1. Check for unlocked doors/windows (safely)\n2. Look for spare keys (under mats, with neighbors, etc.)\n3. Find help - neighbors, nearby businesses\n4. Contact locksmith or family member\n5. Consider temporary shelter if it's late\n\nI should prioritize safe and legal options.",
            "output": "Here's a systematic approach to this problem:\n\n1. **Check for other entry points**: Look for unlocked doors or windows (ground floor only for safety)\n2. **Look for spare keys**: Check under mats, planters, or with trusted neighbors\n3. **Seek help**: Ask neighbors to use their phone to call a locksmith or family member\n4. **Visit nearby businesses**: They might let you use a phone or charge your phone\n5. **Call a locksmith**: If you can reach one, though this will cost money\n6. **Contact family/friends**: Someone might have a spare key\n\nAlways prioritize safety and avoid breaking in, which could be illegal or dangerous.",
            "requires_thinking": True
        },
        {
            "instruction": "How would you organize a successful study group?",
            "thinking": "Organizing a successful study group requires planning and structure. Let me think through the key elements:\n\n1. Group composition - right size and mix of people\n2. Setting clear goals and expectations\n3. Choosing appropriate location and time\n4. Establishing ground rules\n5. Planning structure for meetings\n6. Assigning roles or responsibilities\n7. Maintaining focus and productivity",
            "output": "To organize a successful study group:\n\n**Setup:**\n- Keep it small (3-5 people) with committed members\n- Choose a quiet, comfortable location with minimal distractions\n- Set regular meeting times that work for everyone\n\n**Structure:**\n- Set clear goals for each session\n- Create an agenda and stick to it\n- Assign different topics to different members to teach\n- Take breaks to maintain focus\n\n**Ground Rules:**\n- Come prepared with materials and questions\n- Stay on topic and minimize social chat during study time\n- Respect different learning styles\n- Share resources and help each other\n\nRegular evaluation and adjustment will help maintain effectiveness.",
            "requires_thinking": True
        }
    ]
    return tasks


def main():
    """Create comprehensive training dataset."""
    logger.info("Creating comprehensive training dataset...")

    # Collect all samples
    all_samples = []

    # Add different types of problems
    all_samples.extend(create_math_problems())
    all_samples.extend(create_reasoning_problems())
    all_samples.extend(create_creative_tasks())
    all_samples.extend(create_factual_questions())
    all_samples.extend(create_explanation_tasks())

    # Add more diverse content
    all_samples.extend(create_coding_problems())
    all_samples.extend(create_language_tasks())
    all_samples.extend(create_problem_solving_tasks())

    # Shuffle the samples
    random.shuffle(all_samples)

    logger.info(f"Created {len(all_samples)} training samples")

    # Split into train/validation
    split_idx = int(0.9 * len(all_samples))
    train_samples = all_samples[:split_idx]
    val_samples = all_samples[split_idx:]

    # Save training data
    train_path = "data/train_improved.jsonl"
    with open(train_path, 'w', encoding='utf-8') as f:
        for sample in train_samples:
            f.write(json.dumps(sample) + '\n')

    # Save validation data
    val_path = "data/val_improved.jsonl"
    with open(val_path, 'w', encoding='utf-8') as f:
        for sample in val_samples:
            f.write(json.dumps(sample) + '\n')

    logger.info(f"Saved {len(train_samples)} training samples to {train_path}")
    logger.info(f"Saved {len(val_samples)} validation samples to {val_path}")

    # Print statistics
    thinking_samples = sum(1 for s in train_samples if s['requires_thinking'])
    non_thinking_samples = len(train_samples) - thinking_samples

    logger.info(f"Training set statistics:")
    logger.info(f"  - Thinking samples: {thinking_samples}")
    logger.info(f"  - Non-thinking samples: {non_thinking_samples}")
    logger.info(f"  - Thinking ratio: {thinking_samples/len(train_samples):.2f}")

    # Show sample
    logger.info("\nSample training example:")
    sample = train_samples[0]
    logger.info(f"Instruction: {sample['instruction']}")
    if sample['thinking']:
        logger.info(f"Thinking: {sample['thinking'][:100]}...")
    logger.info(f"Output: {sample['output'][:100]}...")


if __name__ == "__main__":
    main()
