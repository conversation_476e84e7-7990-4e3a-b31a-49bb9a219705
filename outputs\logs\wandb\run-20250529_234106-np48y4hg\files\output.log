INFO:__main__:Starting training...
INFO:thinker_llm.training.trainer:Starting training...
INFO:thinker_llm.training.trainer:Total epochs: 1
INFO:thinker_llm.training.trainer:Total steps: 19944
Epoch 1/1:   2%|█▊                                                                                       | 395/19944 [00:56<45:21,  7.18it/s, loss=2.1276, lr=4.95e-06]INFO:thinker_llm.training.trainer:train/loss: 2.1276
INFO:thinker_llm.training.trainer:train/learning_rate: 0.0000
INFO:thinker_llm.training.trainer:train/global_step: 99.0000
Epoch 1/1:   2%|█▊                                                                                       | 396/19944 [00:56<46:54,  6.95it/s, loss=2.3435, lr=4.95e-06]INFO:thinker_llm.training.trainer:train/loss: 2.3435
INFO:thinker_llm.training.trainer:train/learning_rate: 0.0000
INFO:thinker_llm.training.trainer:train/global_step: 99.0000
Epoch 1/1:   2%|█▊                                                                                       | 397/19944 [00:56<46:11,  7.05it/s, loss=2.3127, lr=4.95e-06]INFO:thinker_llm.training.trainer:train/loss: 2.3127
INFO:thinker_llm.training.trainer:train/learning_rate: 0.0000
INFO:thinker_llm.training.trainer:train/global_step: 99.0000
Epoch 1/1:   2%|█▊                                                                                       | 398/19944 [00:56<45:57,  7.09it/s, loss=2.4540, lr=4.95e-06]INFO:thinker_llm.training.trainer:train/loss: 2.4540
INFO:thinker_llm.training.trainer:train/learning_rate: 0.0000
INFO:thinker_llm.training.trainer:train/global_step: 99.0000
INFO:__main__:Training interrupted by user                                                                                                                             
INFO:thinker_llm.training.trainer:Checkpoint saved: ./outputs\checkpoint_step_129.pt
INFO:__main__:Checkpoint saved to ./outputs\interrupted_checkpoint.pt
