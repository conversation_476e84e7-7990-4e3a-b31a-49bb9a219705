# 🚀 ThinkerLLM Training Guide

This guide will help you train ThinkerLLM on your own data step by step.

## 📋 Prerequisites

1. **Python Environment**: Python 3.8+ with conda environment named 'tts'
2. **Dependencies**: Install requirements from `requirements.txt`
3. **Data**: Your training data in any common format (CSV, JSON, JSONL)
4. **Hardware**: GPU recommended for training (CPU works but slower)

## 🔧 Step 1: Activate Environment and Install

```bash
# Activate your conda environment
conda activate tts

# Install dependencies
pip install -r requirements.txt

# Install ThinkerLLM in development mode
pip install -e .
```

## 📊 Step 2: Prepare Your Data

### Data Format Required

ThinkerLLM expects JSONL format with these fields:

```json
{
    "instruction": "Your question or prompt",
    "thinking": "Step-by-step reasoning process (optional)",
    "output": "Expected response",
    "requires_thinking": true
}
```

### Convert Your Data

Use the provided script to convert your data:

```bash
# From CSV
python scripts/prepare_training_data.py \
    --input your_data.csv \
    --output data/train.jsonl \
    --format csv \
    --instruction-col "question" \
    --output-col "answer" \
    --add-thinking \
    --thinking-ratio 0.3 \
    --validate

# From JSON
python scripts/prepare_training_data.py \
    --input your_data.json \
    --output data/train.jsonl \
    --format json \
    --instruction-field "prompt" \
    --output-field "response" \
    --add-thinking \
    --validate
```

### Example Data Conversion

If your CSV looks like this:
```csv
question,answer,complexity
"What is 2+2?","2+2 equals 4",simple
"Explain photosynthesis","Photosynthesis is...",complex
```

The script will convert it to:
```json
{"instruction": "What is 2+2?", "thinking": "", "output": "2+2 equals 4", "requires_thinking": false}
{"instruction": "Explain photosynthesis", "thinking": "This requires a clear explanation...", "output": "Photosynthesis is...", "requires_thinking": true}
```

## ⚙️ Step 3: Configure Training

### Option A: Use Default Configuration

Copy and modify the default config:

```bash
cp configs/default_config.yaml configs/my_training_config.yaml
```

Edit key parameters in `configs/my_training_config.yaml`:

```yaml
# Model size (adjust based on your resources)
model:
  hidden_size: 512        # Smaller for faster training
  num_layers: 6           # Fewer layers for development
  thinker_hidden_size: 768
  thinker_num_layers: 4

# Training parameters
training:
  batch_size: 4           # Adjust based on GPU memory
  learning_rate: 5.0e-5
  num_epochs: 3
  thinking_ratio: 0.3     # 30% of samples use thinking
  
# Data paths
data:
  train_data_path: "./data/train.jsonl"
  val_data_path: "./data/val.jsonl"
  tokenizer_name: "gpt2"
```

### Option B: Quick Start with Small Model

For development/testing, use a small model:

```yaml
model:
  hidden_size: 256
  num_layers: 4
  thinker_hidden_size: 384
  thinker_num_layers: 3
  use_flash_attention: false  # For CPU training

training:
  batch_size: 2
  num_epochs: 1
  learning_rate: 1.0e-4
```

## 🏃‍♂️ Step 4: Start Training

### Basic Training

```bash
python scripts/train_thinker_llm.py \
    --config configs/my_training_config.yaml \
    --train-data data/train.jsonl \
    --val-data data/val.jsonl \
    --output-dir ./outputs/my_model \
    --model-name my_thinker_llm
```

### Training with Different Strategies

```bash
# Joint training (all components together) - DEFAULT
python scripts/train_thinker_llm.py \
    --config configs/my_training_config.yaml \
    --train-data data/train.jsonl \
    --strategy joint

# Train only thinker and decision (freeze main LLM)
python scripts/train_thinker_llm.py \
    --config configs/my_training_config.yaml \
    --train-data data/train.jsonl \
    --strategy frozen_llm

# Train only main LLM and decision (freeze thinker)
python scripts/train_thinker_llm.py \
    --config configs/my_training_config.yaml \
    --train-data data/train.jsonl \
    --strategy frozen_thinker
```

### Quick Training with Overrides

```bash
python scripts/train_thinker_llm.py \
    --config configs/default_config.yaml \
    --train-data data/train.jsonl \
    --batch-size 8 \
    --learning-rate 3e-5 \
    --num-epochs 2 \
    --thinking-ratio 0.4
```

## 📈 Step 5: Monitor Training

### Training Logs

Monitor training progress:

```bash
# Watch training logs
tail -f outputs/my_model/logs/training.log

# Check tensorboard (if enabled)
tensorboard --logdir outputs/my_model/logs
```

### Weights & Biases (Optional)

If you have wandb configured:

```bash
# Training will automatically log to wandb
# View at https://wandb.ai/your-username/thinker-llm
```

## 🧪 Step 6: Test Your Model

### Quick Test

```python
from thinker_llm.utils.model_utils import load_model
from thinker_llm.inference.pipeline import ThinkerLLMPipeline
from thinker_llm.inference.config import InferenceConfig

# Load your trained model
loaded = load_model("./outputs/my_model/my_thinker_llm")
model = loaded["model"]
tokenizer = loaded["tokenizer"]

# Create inference pipeline
pipeline = ThinkerLLMPipeline(
    model=model,
    tokenizer=tokenizer,
    config=InferenceConfig(show_thinking=True)
)

# Test generation
result = pipeline.generate("What is the capital of France?")
print("Response:", result["response"])
print("Used thinking:", result["used_thinking"])
if result.get("thoughts"):
    print("Thoughts:", result["thoughts"])
```

### Comprehensive Evaluation

```python
# Test multiple examples
test_prompts = [
    "What is 15 + 27?",  # Should trigger thinking
    "What color is the sky?",  # Should not trigger thinking
    "Explain how machine learning works",  # Should trigger thinking
]

for prompt in test_prompts:
    result = pipeline.generate(prompt, return_thoughts=True)
    print(f"\nPrompt: {prompt}")
    print(f"Used thinking: {result['used_thinking']}")
    print(f"Response: {result['response'][:100]}...")
```

## 🔧 Troubleshooting

### Common Issues

1. **Out of Memory Error**
   ```bash
   # Reduce batch size
   --batch-size 2
   
   # Use gradient accumulation
   # Edit config: gradient_accumulation_steps: 4
   ```

2. **Slow Training**
   ```bash
   # Use smaller model
   # Edit config: hidden_size: 256, num_layers: 4
   
   # Disable flash attention on CPU
   # Edit config: use_flash_attention: false
   ```

3. **Poor Performance**
   ```bash
   # Increase thinking ratio
   --thinking-ratio 0.5
   
   # Train longer
   --num-epochs 5
   
   # Adjust learning rate
   --learning-rate 3e-5
   ```

### Resume Training

If training is interrupted:

```bash
python scripts/train_thinker_llm.py \
    --config configs/my_training_config.yaml \
    --train-data data/train.jsonl \
    --resume outputs/my_model/checkpoint_step_1000.pt
```

## 📊 Data Requirements

### Minimum Data Size
- **Small model**: 1,000+ samples
- **Medium model**: 10,000+ samples  
- **Large model**: 100,000+ samples

### Thinking Ratio
- **Conservative**: 20-30% thinking samples
- **Balanced**: 30-50% thinking samples
- **Aggressive**: 50%+ thinking samples

### Data Quality Tips
1. **Clear instructions**: Make questions/prompts clear and specific
2. **Good thinking examples**: Include step-by-step reasoning
3. **Diverse complexity**: Mix simple and complex examples
4. **Consistent format**: Keep formatting consistent across samples

## 🎯 Next Steps

After training:

1. **Evaluate Performance**: Test on held-out data
2. **Fine-tune**: Adjust hyperparameters based on results
3. **Scale Up**: Train larger models with more data
4. **Deploy**: Use the inference pipeline in your applications

## 💡 Tips for Success

1. **Start Small**: Begin with a small model and dataset
2. **Monitor Closely**: Watch training metrics and adjust
3. **Iterate**: Train multiple versions with different settings
4. **Validate**: Always test on unseen data
5. **Document**: Keep track of what works and what doesn't

Need help with any specific step? The implementation includes comprehensive logging and error handling to guide you through the process!
