"""
Configuration classes for ThinkerLLM components.
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
import json
import yaml
from pathlib import Path


@dataclass
class ModelConfig:
    """Configuration for model architecture."""

    # Main LLM Configuration
    vocab_size: int = 50257
    hidden_size: int = 768
    num_layers: int = 12
    num_attention_heads: int = 12
    intermediate_size: int = 3072
    max_position_embeddings: int = 2048
    dropout_prob: float = 0.1
    layer_norm_eps: float = 1e-5

    # ThinkerModule Configuration
    thinker_hidden_size: int = 1024
    thinker_num_layers: int = 8
    thinker_num_heads: int = 16
    thinker_intermediate_size: int = 4096
    thinker_max_length: int = 512
    thinker_dropout: float = 0.1

    # Decision Mechanism Configuration
    decision_hidden_size: int = 512
    decision_num_layers: int = 3
    decision_threshold: float = 0.5
    decision_features: List[str] = field(default_factory=lambda: [
        "input_length", "complexity_score", "question_type"
    ])

    # Projection Layer Configuration
    projection_type: str = "adaptive"  # "linear", "adaptive", "cross_attention"
    projection_hidden_size: int = 512
    projection_num_layers: int = 2

    # General Configuration
    use_flash_attention: bool = True
    gradient_checkpointing: bool = False
    tie_word_embeddings: bool = True

    def save(self, path: str):
        """Save configuration to file."""
        path = Path(path)
        if path.suffix == '.json':
            with open(path, 'w') as f:
                json.dump(self.__dict__, f, indent=2)
        elif path.suffix in ['.yaml', '.yml']:
            with open(path, 'w') as f:
                yaml.dump(self.__dict__, f, default_flow_style=False)
        else:
            raise ValueError(f"Unsupported file format: {path.suffix}")

    @classmethod
    def load(cls, path: str) -> 'ModelConfig':
        """Load configuration from file."""
        path = Path(path)
        if path.suffix == '.json':
            with open(path, 'r') as f:
                config_dict = json.load(f)
        elif path.suffix in ['.yaml', '.yml']:
            with open(path, 'r') as f:
                config_dict = yaml.safe_load(f)
        else:
            raise ValueError(f"Unsupported file format: {path.suffix}")

        return cls(**config_dict)


@dataclass
class TrainingConfig:
    """Configuration for training process."""

    # Training Parameters
    batch_size: int = 8
    learning_rate: float = 5e-5
    num_epochs: int = 3
    warmup_steps: int = 1000
    max_grad_norm: float = 1.0
    weight_decay: float = 0.01

    # Loss Weights
    llm_loss_weight: float = 1.0
    thinker_loss_weight: float = 0.5
    decision_loss_weight: float = 0.3

    # Training Strategy
    joint_training: bool = True
    pretrain_components: bool = False
    freeze_llm: bool = False
    freeze_thinker: bool = False

    # Optimization
    optimizer: str = "adamw"
    scheduler: str = "cosine"
    gradient_accumulation_steps: int = 1

    # Mixed Precision Training
    use_amp: bool = True  # Enable automatic mixed precision
    mixed_precision_dtype: str = "bf16"  # "fp16" or "bf16"

    # Logging and Checkpointing
    logging_steps: int = 100
    eval_steps: int = 500
    save_steps: int = 1000
    save_total_limit: int = 3

    # Data
    max_length: int = 2048
    thinking_ratio: float = 0.3  # Proportion of samples requiring thinking

    # Paths
    output_dir: str = "./outputs"
    logging_dir: str = "./logs"
    cache_dir: str = "./cache"

    def save(self, path: str):
        """Save configuration to file."""
        path = Path(path)
        if path.suffix == '.json':
            with open(path, 'w') as f:
                json.dump(self.__dict__, f, indent=2)
        elif path.suffix in ['.yaml', '.yml']:
            with open(path, 'w') as f:
                yaml.dump(self.__dict__, f, default_flow_style=False)
        else:
            raise ValueError(f"Unsupported file format: {path.suffix}")

    @classmethod
    def load(cls, path: str) -> 'TrainingConfig':
        """Load configuration from file."""
        path = Path(path)
        if path.suffix == '.json':
            with open(path, 'r') as f:
                config_dict = json.load(f)
        elif path.suffix in ['.yaml', '.yml']:
            with open(path, 'r') as f:
                config_dict = yaml.safe_load(f)
        else:
            raise ValueError(f"Unsupported file format: {path.suffix}")

        return cls(**config_dict)


@dataclass
class InferenceConfig:
    """Configuration for inference process."""

    # Generation Parameters
    max_new_tokens: int = 512
    temperature: float = 0.7
    top_p: float = 0.9
    top_k: int = 50
    do_sample: bool = True
    num_beams: int = 1

    # Thinking Parameters
    show_thinking: bool = True
    thinking_temperature: float = 0.8
    max_thinking_tokens: int = 256

    # Decision Parameters
    decision_threshold: float = 0.5
    force_thinking: bool = False
    disable_thinking: bool = False

    # Performance
    batch_size: int = 1
    use_cache: bool = True

    def save(self, path: str):
        """Save configuration to file."""
        path = Path(path)
        if path.suffix == '.json':
            with open(path, 'w') as f:
                json.dump(self.__dict__, f, indent=2)
        elif path.suffix in ['.yaml', '.yml']:
            with open(path, 'w') as f:
                yaml.dump(self.__dict__, f, default_flow_style=False)
        else:
            raise ValueError(f"Unsupported file format: {path.suffix}")

    @classmethod
    def load(cls, path: str) -> 'InferenceConfig':
        """Load configuration from file."""
        path = Path(path)
        if path.suffix == '.json':
            with open(path, 'r') as f:
                config_dict = json.load(f)
        elif path.suffix in ['.yaml', '.yml']:
            with open(path, 'r') as f:
                config_dict = yaml.safe_load(f)
        else:
            raise ValueError(f"Unsupported file format: {path.suffix}")

        return cls(**config_dict)
