{"os": "Windows-11-10.0.26100-SP0", "python": "CPython 3.12.9", "startedAt": "2025-05-29T18:09:43.821863Z", "program": "D:\\vlinhd11\\Projects\\LCMS_LLM\\MOE-LLM\\geneticAI\\v4\\train_improved_thinker.py", "codePath": "train_improved_thinker.py", "email": "<EMAIL>", "root": "./outputs/improved_training/logs", "host": "VLINHD11-WORK", "executable": "D:\\.conda\\envs\\tts\\python.exe", "codePathLocal": "train_improved_thinker.py", "cpu_count": 10, "cpu_count_logical": 20, "gpu": "NVIDIA GeForce RTX 4070", "gpu_count": 1, "disk": {"/": {"total": "540765319168", "used": "431034200064"}}, "memory": {"total": "34165837824"}, "cpu": {"count": 10, "countLogical": 20}, "gpu_nvidia": [{"name": "NVIDIA GeForce RTX 4070", "memoryTotal": "12878610432", "cudaCores": 5888, "architecture": "Ada"}], "cudaVersion": "12.6"}