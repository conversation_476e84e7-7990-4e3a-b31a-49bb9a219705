2025-05-29 16:52:43,558 INFO    MainThread:29416 [wandb_setup.py:_flush():67] Current SDK version is 0.19.8
2025-05-29 16:52:43,558 INFO    MainThread:29416 [wandb_setup.py:_flush():67] Configure stats pid to 29416
2025-05-29 16:52:43,558 INFO    MainThread:29416 [wandb_setup.py:_flush():67] Loading settings from C:\Users\<USER>\.config\wandb\settings
2025-05-29 16:52:43,558 INFO    MainThread:29416 [wandb_setup.py:_flush():67] Loading settings from D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\wandb\settings
2025-05-29 16:52:43,558 INFO    MainThread:29416 [wandb_setup.py:_flush():67] Loading settings from environment variables
2025-05-29 16:52:43,558 INFO    MainThread:29416 [wandb_init.py:setup_run_log_directory():647] Logging user logs to ./outputs\logs\wandb\run-20250529_165243-kw8h85fg\logs\debug.log
2025-05-29 16:52:43,559 INFO    MainThread:29416 [wandb_init.py:setup_run_log_directory():648] Logging internal logs to ./outputs\logs\wandb\run-20250529_165243-kw8h85fg\logs\debug-internal.log
2025-05-29 16:52:43,559 INFO    MainThread:29416 [wandb_init.py:init():761] calling init triggers
2025-05-29 16:52:43,559 INFO    MainThread:29416 [wandb_init.py:init():766] wandb.init called with sweep_config: {}
config: {'batch_size': 2, 'learning_rate': 3e-05, 'num_epochs': 1, 'warmup_steps': 200, 'max_grad_norm': 1.0, 'weight_decay': 0.01, 'llm_loss_weight': 1.0, 'thinker_loss_weight': 0.6, 'decision_loss_weight': 0.4, 'joint_training': True, 'pretrain_components': False, 'freeze_llm': False, 'freeze_thinker': False, 'optimizer': 'adamw', 'scheduler': 'cosine', 'gradient_accumulation_steps': 2, 'logging_steps': 50, 'eval_steps': 200, 'save_steps': 500, 'save_total_limit': 3, 'max_length': 1024, 'thinking_ratio': 0.35, 'output_dir': './outputs', 'logging_dir': './outputs\\logs', 'cache_dir': './cache', '_wandb': {}}
2025-05-29 16:52:43,559 INFO    MainThread:29416 [wandb_init.py:init():784] starting backend
2025-05-29 16:52:43,559 INFO    MainThread:29416 [wandb_init.py:init():788] sending inform_init request
2025-05-29 16:52:43,602 INFO    MainThread:29416 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=spawn, using: spawn
2025-05-29 16:52:43,602 INFO    MainThread:29416 [wandb_init.py:init():798] backend started and connected
2025-05-29 16:52:43,604 INFO    MainThread:29416 [wandb_init.py:init():891] updated telemetry
2025-05-29 16:52:43,606 INFO    MainThread:29416 [wandb_init.py:init():915] communicating run to backend with 90.0 second timeout
2025-05-29 16:52:44,121 INFO    MainThread:29416 [wandb_init.py:init():990] starting run threads in backend
2025-05-29 16:52:44,405 INFO    MainThread:29416 [wandb_run.py:_console_start():2375] atexit reg
2025-05-29 16:52:44,406 INFO    MainThread:29416 [wandb_run.py:_redirect():2227] redirect: wrap_raw
2025-05-29 16:52:44,406 INFO    MainThread:29416 [wandb_run.py:_redirect():2292] Wrapping output streams.
2025-05-29 16:52:44,406 INFO    MainThread:29416 [wandb_run.py:_redirect():2315] Redirects installed.
2025-05-29 16:52:44,407 INFO    MainThread:29416 [wandb_init.py:init():1032] run started, returning control to user process
2025-05-29 16:52:44,720 INFO    MsgRouterThr:29416 [mailbox.py:close():129] Closing mailbox, abandoning 1 handles.
