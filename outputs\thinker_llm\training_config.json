{"batch_size": 1, "learning_rate": 5e-05, "num_epochs": 1, "warmup_steps": 50, "max_grad_norm": 1.0, "weight_decay": 0.01, "llm_loss_weight": 1.0, "thinker_loss_weight": 0.6, "decision_loss_weight": 0.4, "joint_training": true, "pretrain_components": false, "freeze_llm": false, "freeze_thinker": false, "optimizer": "adamw", "scheduler": "cosine", "gradient_accumulation_steps": 2, "logging_steps": 50, "eval_steps": 200, "save_steps": 500, "save_total_limit": 3, "max_length": 512, "thinking_ratio": 0.35, "output_dir": "./outputs", "logging_dir": "./outputs\\logs", "cache_dir": "./cache"}