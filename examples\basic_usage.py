"""
Basic usage example for ThinkerLLM.

This script demonstrates how to:
1. <PERSON>reate and configure a ThinkerLLM model
2. Generate synthetic training data
3. Train the model
4. Use the model for inference
"""

import torch
from transformers import AutoTokenizer
import logging

# Import ThinkerLLM components
from thinker_llm.models import IntegratedThinkerLLM
from thinker_llm.utils.config import ModelConfig, TrainingConfig
from thinker_llm.training.data_loader import create_synthetic_data, prepare_dataset
from thinker_llm.training.trainer import Thinker<PERSON><PERSON>rainer
from thinker_llm.inference.pipeline import Thinker<PERSON><PERSON><PERSON>eline
from thinker_llm.inference.config import InferenceConfig
from thinker_llm.utils.model_utils import save_model, load_model

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def main():
    """Main function demonstrating ThinkerLLM usage."""
    
    # 1. Configuration
    logger.info("Setting up configurations...")
    
    # Model configuration
    model_config = ModelConfig(
        vocab_size=50257,  # GPT-2 vocab size
        hidden_size=512,   # Smaller for demo
        num_layers=6,
        num_attention_heads=8,
        thinker_hidden_size=768,
        thinker_num_layers=4,
        max_position_embeddings=1024,
    )
    
    # Training configuration
    training_config = TrainingConfig(
        batch_size=4,
        learning_rate=5e-5,
        num_epochs=2,
        warmup_steps=100,
        max_length=512,
        thinking_ratio=0.3,
        output_dir="./outputs",
        logging_dir="./logs",
    )
    
    # Inference configuration
    inference_config = InferenceConfig(
        max_new_tokens=256,
        temperature=0.7,
        show_thinking=True,
    )
    
    # 2. Create synthetic training data
    logger.info("Creating synthetic training data...")
    create_synthetic_data(
        num_samples=100,  # Small dataset for demo
        output_path="./data/synthetic_train.jsonl",
        thinking_ratio=0.3,
    )
    
    # 3. Initialize model and tokenizer
    logger.info("Initializing model and tokenizer...")
    
    tokenizer = AutoTokenizer.from_pretrained("gpt2")
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    model = IntegratedThinkerLLM(model_config)
    
    # Print model info
    model_info = model.get_model_info()
    logger.info(f"Model initialized with {model_info['total_parameters']:,} parameters")
    
    # 4. Prepare data
    logger.info("Preparing training data...")
    
    train_dataset, train_dataloader = prepare_dataset(
        data_path="./data/synthetic_train.jsonl",
        tokenizer_name="gpt2",
        max_length=training_config.max_length,
        thinking_ratio=training_config.thinking_ratio,
        batch_size=training_config.batch_size,
        split="train",
    )
    
    logger.info(f"Training dataset size: {len(train_dataset)}")
    
    # 5. Training (optional - can be skipped for inference-only demo)
    train_model = input("Do you want to train the model? (y/n): ").lower() == 'y'
    
    if train_model:
        logger.info("Starting training...")
        
        trainer = ThinkerLLMTrainer(
            model=model,
            train_dataloader=train_dataloader.get_dataloader(),
            config=training_config,
            model_config=model_config,
        )
        
        # Train the model
        trainer.train()
        
        # Save the trained model
        logger.info("Saving trained model...")
        save_model(
            model=model,
            tokenizer=tokenizer,
            save_path="./models/thinker_llm_demo",
            model_config=model_config,
            training_config=training_config,
        )
    
    # 6. Inference demonstration
    logger.info("Setting up inference pipeline...")
    
    # If we didn't train, we'll use the untrained model for demo
    pipeline = ThinkerLLMPipeline(
        model=model,
        tokenizer=tokenizer,
        config=inference_config,
    )
    
    # Example prompts
    example_prompts = [
        "What is the capital of France?",  # Simple factual question
        "Explain how photosynthesis works and why it's important for life on Earth.",  # Complex reasoning
        "Solve this step by step: If a train travels 120 miles in 2 hours, what is its average speed?",  # Math problem
        "Compare the advantages and disadvantages of renewable energy sources.",  # Analysis task
    ]
    
    logger.info("Running inference examples...")
    
    for i, prompt in enumerate(example_prompts, 1):
        print(f"\n{'='*60}")
        print(f"Example {i}: {prompt}")
        print('='*60)
        
        # Generate response
        result = pipeline.generate(
            text=prompt,
            return_thoughts=True,
            max_new_tokens=200,
        )
        
        # Display results
        print(f"Used Thinking: {result['used_thinking']}")
        print(f"Thinking Probability: {result['thinking_probability']:.3f}")
        
        if result.get('thoughts'):
            print(f"\nThoughts:\n{result['thoughts']}")
        
        print(f"\nResponse:\n{result['response']}")
        print(f"\nGeneration Time: {result['generation_time']:.2f}s")
    
    # 7. Decision analysis demonstration
    logger.info("\nAnalyzing decision mechanism...")
    
    analysis_prompt = "Should I invest in cryptocurrency?"
    decision_analysis = pipeline.analyze_decision(analysis_prompt)
    
    print(f"\n{'='*60}")
    print("Decision Analysis")
    print('='*60)
    print(f"Prompt: {analysis_prompt}")
    print(f"Should Think: {decision_analysis['should_think']}")
    print(f"Thinking Probability: {decision_analysis['thinking_probability']:.3f}")
    print(f"Question Type Distribution: {decision_analysis['question_type']}")
    
    # 8. Batch processing demonstration
    logger.info("\nDemonstrating batch processing...")
    
    batch_prompts = [
        "What is 2+2?",
        "Explain quantum computing",
        "How do I bake a cake?",
    ]
    
    batch_results = pipeline.batch_generate(
        texts=batch_prompts,
        batch_size=2,
        return_thoughts=False,  # Skip thoughts for faster processing
        max_new_tokens=100,
    )
    
    print(f"\n{'='*60}")
    print("Batch Processing Results")
    print('='*60)
    
    for prompt, result in zip(batch_prompts, batch_results):
        print(f"\nPrompt: {prompt}")
        print(f"Response: {result['response'][:100]}...")
        print(f"Used Thinking: {result['used_thinking']}")
    
    logger.info("Demo completed successfully!")


if __name__ == "__main__":
    main()
