#!/usr/bin/env python3
"""
Debug script to identify data loading issues.
"""

import sys
import os
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent))

import torch
import json
from transformers import AutoTokenizer
from thinker_llm.training.data_loader import ThinkerDataset, ThinkerDataLoader
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_data_format(data_path: str):
    """Check the format of the data file."""
    logger.info(f"Checking data format for: {data_path}")
    
    try:
        with open(data_path, 'r', encoding='utf-8') as f:
            first_line = f.readline().strip()
            sample = json.loads(first_line)
            
        logger.info(f"Sample keys: {list(sample.keys())}")
        logger.info(f"Sample instruction length: {len(sample.get('instruction', ''))}")
        logger.info(f"Sample thinking length: {len(sample.get('thinking', ''))}")
        logger.info(f"Sample output length: {len(sample.get('output', ''))}")
        logger.info(f"Requires thinking: {sample.get('requires_thinking', 'Not specified')}")
        
        # Count total lines
        with open(data_path, 'r', encoding='utf-8') as f:
            total_lines = sum(1 for _ in f)
        logger.info(f"Total samples: {total_lines}")
        
        return True
    except Exception as e:
        logger.error(f"Error reading data file: {e}")
        return False

def test_tokenizer_setup():
    """Test tokenizer setup and special tokens."""
    logger.info("Testing tokenizer setup...")
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained("gpt2")
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    logger.info(f"Initial tokenizer vocab size: {len(tokenizer)}")
    
    # Add special tokens
    special_tokens = ["<|thinking|>", "<|/thinking|>", "<|response|>", "<|/response|>"]
    new_tokens = []
    for token in special_tokens:
        if token not in tokenizer.get_vocab():
            new_tokens.append(token)
    
    if new_tokens:
        num_added = tokenizer.add_tokens(new_tokens)
        logger.info(f"Added {num_added} special tokens")
    
    logger.info(f"Final tokenizer vocab size: {len(tokenizer)}")
    
    # Test token IDs
    for token in special_tokens:
        token_id = tokenizer.convert_tokens_to_ids(token)
        logger.info(f"Token '{token}' -> ID {token_id}")
        if token_id >= len(tokenizer):
            logger.error(f"Token ID {token_id} is out of bounds for vocab size {len(tokenizer)}")
    
    return tokenizer

def test_dataset_loading(data_path: str, tokenizer):
    """Test dataset loading with the given tokenizer."""
    logger.info(f"Testing dataset loading for: {data_path}")
    
    try:
        dataset = ThinkerDataset(
            data_path=data_path,
            tokenizer=tokenizer,
            max_length=512,  # Smaller for testing
            thinking_ratio=0.3,
            split="train",
        )
        
        logger.info(f"Dataset created successfully with {len(dataset)} samples")
        
        # Test first sample
        sample = dataset[0]
        logger.info(f"Sample keys: {list(sample.keys())}")
        
        for key, value in sample.items():
            if isinstance(value, torch.Tensor):
                logger.info(f"{key}: shape={value.shape}, dtype={value.dtype}")
                if key in ['input_ids', 'labels', 'thinking_ids']:
                    min_val = value.min().item()
                    max_val = value.max().item()
                    logger.info(f"  {key} range: {min_val} to {max_val}")
                    
                    # Check for out-of-bounds values
                    if max_val >= len(tokenizer):
                        logger.error(f"Found token ID {max_val} >= vocab size {len(tokenizer)} in {key}")
                    if min_val < -100 and key != 'labels':
                        logger.error(f"Found unexpected negative value {min_val} in {key}")
            else:
                logger.info(f"{key}: {value}")
        
        return dataset
        
    except Exception as e:
        logger.error(f"Error creating dataset: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_dataloader(dataset):
    """Test dataloader with the dataset."""
    logger.info("Testing dataloader...")
    
    try:
        dataloader = ThinkerDataLoader(
            dataset=dataset,
            batch_size=2,
            shuffle=False,
        )
        
        data_iter = iter(dataloader.get_dataloader())
        batch = next(data_iter)
        
        logger.info(f"Batch keys: {list(batch.keys())}")
        for key, value in batch.items():
            if isinstance(value, torch.Tensor):
                logger.info(f"{key}: shape={value.shape}, dtype={value.dtype}")
                min_val = value.min().item()
                max_val = value.max().item()
                logger.info(f"  {key} range: {min_val} to {max_val}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error with dataloader: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main debug function."""
    logger.info("Starting data loading debug...")
    
    # Test both data files
    data_files = ["safe_data/train.jsonl", "data/train.jsonl"]
    
    for data_path in data_files:
        if not os.path.exists(data_path):
            logger.warning(f"Data file not found: {data_path}")
            continue
            
        logger.info(f"\n{'='*50}")
        logger.info(f"Testing: {data_path}")
        logger.info(f"{'='*50}")
        
        # Check data format
        if not check_data_format(data_path):
            continue
        
        # Test tokenizer
        tokenizer = test_tokenizer_setup()
        
        # Test dataset
        dataset = test_dataset_loading(data_path, tokenizer)
        if dataset is None:
            continue
        
        # Test dataloader
        test_dataloader(dataset)
        
        logger.info(f"Completed testing: {data_path}")

if __name__ == "__main__":
    main()
