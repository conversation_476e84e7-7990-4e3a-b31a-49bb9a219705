INFO:__main__:Starting training...
INFO:thinker_llm.training.trainer:Starting training...
INFO:thinker_llm.training.trainer:Total epochs: 1
INFO:thinker_llm.training.trainer:Total steps: 19944
Epoch 1/1:   0%|                                                                                        | 0/19944 [00:05<?, ?it/s, loss=4.2153, lr=0.00e+00]INFO:thinker_llm.training.trainer:train/loss: 4.2153
INFO:thinker_llm.training.trainer:train/learning_rate: 0.0000
INFO:thinker_llm.training.trainer:train/global_step: 0.0000
INFO:thinker_llm.training.trainer:Checkpoint saved: ./outputs\checkpoint_step_0.pt
Epoch 1/1:   0%|                                                                             | 1/19944 [00:19<45:43:38,  8.25s/it, loss=4.2081, lr=0.00e+00]INFO:thinker_llm.training.trainer:train/loss: 4.2081
INFO:thinker_llm.training.trainer:train/learning_rate: 0.0000
INFO:thinker_llm.training.trainer:train/global_step: 0.0000
INFO:thinker_llm.training.trainer:Checkpoint saved: ./outputs\checkpoint_step_0.pt
Epoch 1/1:   0%|                                                                             | 2/19944 [00:30<62:46:39, 11.33s/it, loss=4.2273, lr=0.00e+00]INFO:thinker_llm.training.trainer:train/loss: 4.2273
INFO:thinker_llm.training.trainer:train/learning_rate: 0.0000
INFO:thinker_llm.training.trainer:train/global_step: 0.0000
INFO:thinker_llm.training.trainer:Checkpoint saved: ./outputs\checkpoint_step_0.pt
INFO:__main__:Training interrupted by user                                                                                                                  
INFO:thinker_llm.training.trainer:Checkpoint saved: ./outputs\checkpoint_step_0.pt
INFO:__main__:Checkpoint saved to ./outputs\interrupted_checkpoint.pt
