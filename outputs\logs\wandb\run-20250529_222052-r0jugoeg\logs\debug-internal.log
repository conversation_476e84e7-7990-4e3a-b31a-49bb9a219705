{"time":"2025-05-29T22:20:52.3434473+07:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"outputs\\logs\\wandb\\run-20250529_222052-r0jugoeg\\logs\\debug-core.log"}
{"time":"2025-05-29T22:20:52.4543557+07:00","level":"INFO","msg":"created new stream","id":"r0jugoeg"}
{"time":"2025-05-29T22:20:52.4543557+07:00","level":"INFO","msg":"stream: started","id":"r0jugoeg"}
{"time":"2025-05-29T22:20:52.4543557+07:00","level":"INFO","msg":"handler: started","stream_id":"r0jugoeg"}
{"time":"2025-05-29T22:20:52.4543557+07:00","level":"INFO","msg":"sender: started","stream_id":"r0jugoeg"}
{"time":"2025-05-29T22:20:52.4543557+07:00","level":"INFO","msg":"writer: Do: started","stream_id":"r0jugoeg"}
{"time":"2025-05-29T22:20:52.8590584+07:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-05-29T22:21:07.8699851+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:21:22.8702879+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:21:37.869549+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:21:52.8708766+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:22:07.8691285+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:22:22.8743923+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:22:37.8704965+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:22:52.8723364+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:23:07.870451+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:23:22.8697779+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:23:37.8708845+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:23:52.8695599+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:24:07.8688447+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:24:22.8689337+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:24:37.869769+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:24:52.8789493+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:25:07.8697641+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:25:22.8697434+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:25:37.8715054+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:25:52.8839319+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:26:07.8701144+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:26:22.8703156+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:26:37.8711918+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:26:52.8713233+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:27:07.8721041+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:27:22.8741798+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T22:27:29.6638398+07:00","level":"INFO","msg":"stream: closing","id":"r0jugoeg"}
{"time":"2025-05-29T22:27:29.6638398+07:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-05-29T22:27:29.664358+07:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-05-29T22:27:31.3068249+07:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-05-29T22:27:31.7743412+07:00","level":"INFO","msg":"handler: closed","stream_id":"r0jugoeg"}
{"time":"2025-05-29T22:27:31.7743412+07:00","level":"INFO","msg":"writer: Close: closed","stream_id":"r0jugoeg"}
{"time":"2025-05-29T22:27:31.7743412+07:00","level":"INFO","msg":"sender: closed","stream_id":"r0jugoeg"}
{"time":"2025-05-29T22:27:31.7748462+07:00","level":"INFO","msg":"stream: closed","id":"r0jugoeg"}
