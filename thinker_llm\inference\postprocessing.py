"""
Postprocessing utilities for ThinkerLLM outputs.
"""

import torch
import re
from typing import Dict, List, Optional, Any, Tuple
from transformers import AutoTokenizer


class ThoughtProcessor:
    """
    Processor for cleaning and formatting thought sequences.
    """
    
    def __init__(self, tokenizer: AutoTokenizer):
        self.tokenizer = tokenizer
        
        # Special token patterns
        self.thinking_start_pattern = r"<\|thinking\|>"
        self.thinking_end_pattern = r"<\|/thinking\|>"
        self.response_start_pattern = r"<\|response\|>"
        self.response_end_pattern = r"<\|/response\|>"
    
    def process_thoughts(self, thought_ids: torch.Tensor) -> str:
        """
        Process thought token IDs into clean text.
        
        Args:
            thought_ids: Thought token IDs
            
        Returns:
            Cleaned thought text
        """
        # Decode thoughts
        raw_thoughts = self.tokenizer.decode(
            thought_ids, 
            skip_special_tokens=False,
            clean_up_tokenization_spaces=True
        )
        
        # Clean and format
        cleaned_thoughts = self._clean_thoughts(raw_thoughts)
        formatted_thoughts = self._format_thoughts(cleaned_thoughts)
        
        return formatted_thoughts
    
    def _clean_thoughts(self, raw_thoughts: str) -> str:
        """
        Clean raw thought text.
        
        Args:
            raw_thoughts: Raw thought text
            
        Returns:
            Cleaned thought text
        """
        # Remove special tokens
        text = re.sub(self.thinking_start_pattern, "", raw_thoughts)
        text = re.sub(self.thinking_end_pattern, "", text)
        text = re.sub(self.response_start_pattern, "", text)
        text = re.sub(self.response_end_pattern, "", text)
        
        # Clean up whitespace
        text = re.sub(r'\n\s*\n', '\n\n', text)  # Multiple newlines to double
        text = re.sub(r'[ \t]+', ' ', text)      # Multiple spaces to single
        text = text.strip()
        
        # Remove repetitive patterns
        text = self._remove_repetitions(text)
        
        return text
    
    def _remove_repetitions(self, text: str) -> str:
        """
        Remove repetitive patterns in text.
        
        Args:
            text: Input text
            
        Returns:
            Text with repetitions removed
        """
        # Remove repeated phrases (simple heuristic)
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            # Skip if line is very similar to previous line
            if cleaned_lines:
                prev_line = cleaned_lines[-1]
                # Simple similarity check
                if len(line) > 10 and len(prev_line) > 10:
                    # Check for substantial overlap
                    words1 = set(line.lower().split())
                    words2 = set(prev_line.lower().split())
                    overlap = len(words1 & words2) / max(len(words1 | words2), 1)
                    
                    if overlap > 0.8:  # High overlap, skip
                        continue
            
            cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    def _format_thoughts(self, thoughts: str) -> str:
        """
        Format thoughts for better readability.
        
        Args:
            thoughts: Cleaned thought text
            
        Returns:
            Formatted thought text
        """
        if not thoughts.strip():
            return ""
        
        # Add thinking header
        formatted = "🤔 **Thinking Process:**\n\n"
        
        # Split into paragraphs and format
        paragraphs = thoughts.split('\n\n')
        formatted_paragraphs = []
        
        for i, paragraph in enumerate(paragraphs):
            if paragraph.strip():
                # Add step numbering for structured thoughts
                if self._looks_like_step(paragraph):
                    formatted_paragraphs.append(f"**Step {i+1}:** {paragraph.strip()}")
                else:
                    formatted_paragraphs.append(paragraph.strip())
        
        formatted += '\n\n'.join(formatted_paragraphs)
        
        return formatted
    
    def _looks_like_step(self, text: str) -> bool:
        """
        Check if text looks like a reasoning step.
        
        Args:
            text: Text to check
            
        Returns:
            True if looks like a step
        """
        step_indicators = [
            "first", "second", "third", "next", "then", "finally",
            "step", "let me", "i need to", "i should", "now"
        ]
        
        text_lower = text.lower()
        return any(indicator in text_lower for indicator in step_indicators)
    
    def extract_key_insights(self, thoughts: str) -> List[str]:
        """
        Extract key insights from thoughts.
        
        Args:
            thoughts: Thought text
            
        Returns:
            List of key insights
        """
        insights = []
        
        # Look for conclusion indicators
        conclusion_patterns = [
            r"therefore[,:]?\s*(.+)",
            r"in conclusion[,:]?\s*(.+)",
            r"this means[,:]?\s*(.+)",
            r"so[,:]?\s*(.+)",
            r"thus[,:]?\s*(.+)",
        ]
        
        for pattern in conclusion_patterns:
            matches = re.finditer(pattern, thoughts, re.IGNORECASE)
            for match in matches:
                insight = match.group(1).strip()
                if len(insight) > 10:  # Filter out very short insights
                    insights.append(insight)
        
        return insights[:3]  # Return top 3 insights


class ResponseFormatter:
    """
    Formatter for final response outputs.
    """
    
    def __init__(self, tokenizer: AutoTokenizer):
        self.tokenizer = tokenizer
        
        # Response patterns
        self.response_start_pattern = r"<\|response\|>"
        self.response_end_pattern = r"<\|/response\|>"
    
    def format_response(
        self,
        raw_response: str,
        include_thoughts: bool = False,
        thoughts: str = "",
    ) -> str:
        """
        Format the final response.
        
        Args:
            raw_response: Raw response text
            include_thoughts: Whether to include thoughts
            thoughts: Thought text to include
            
        Returns:
            Formatted response
        """
        # Clean response
        cleaned_response = self._clean_response(raw_response)
        
        # Format final output
        if include_thoughts and thoughts:
            formatted = f"{thoughts}\n\n---\n\n**Response:**\n\n{cleaned_response}"
        else:
            formatted = cleaned_response
        
        return formatted
    
    def _clean_response(self, raw_response: str) -> str:
        """
        Clean raw response text.
        
        Args:
            raw_response: Raw response text
            
        Returns:
            Cleaned response text
        """
        # Remove special tokens
        text = re.sub(self.response_start_pattern, "", raw_response)
        text = re.sub(self.response_end_pattern, "", text)
        
        # Clean up whitespace
        text = re.sub(r'\n\s*\n', '\n\n', text)
        text = re.sub(r'[ \t]+', ' ', text)
        text = text.strip()
        
        # Remove incomplete sentences at the end
        text = self._remove_incomplete_ending(text)
        
        return text
    
    def _remove_incomplete_ending(self, text: str) -> str:
        """
        Remove incomplete sentences at the end of text.
        
        Args:
            text: Input text
            
        Returns:
            Text with incomplete ending removed
        """
        # Split into sentences
        sentences = re.split(r'[.!?]+', text)
        
        if len(sentences) > 1:
            # Check if last sentence looks incomplete
            last_sentence = sentences[-1].strip()
            
            # Heuristics for incomplete sentences
            if (len(last_sentence) < 10 or 
                not last_sentence or
                last_sentence.count(' ') < 2):
                # Remove last incomplete sentence
                complete_sentences = sentences[:-1]
                text = '.'.join(complete_sentences)
                if text and not text.endswith(('.', '!', '?')):
                    text += '.'
        
        return text
    
    def add_metadata(
        self,
        response: str,
        metadata: Dict[str, Any],
    ) -> str:
        """
        Add metadata to response.
        
        Args:
            response: Response text
            metadata: Metadata dictionary
            
        Returns:
            Response with metadata
        """
        if not metadata:
            return response
        
        metadata_text = "\n\n---\n\n**Generation Info:**\n"
        
        # Add relevant metadata
        if "thinking_probability" in metadata:
            prob = metadata["thinking_probability"]
            metadata_text += f"- Thinking Probability: {prob:.2f}\n"
        
        if "generation_time" in metadata:
            time_ms = metadata["generation_time"] * 1000
            metadata_text += f"- Generation Time: {time_ms:.1f}ms\n"
        
        if "used_thinking" in metadata:
            used = "Yes" if metadata["used_thinking"] else "No"
            metadata_text += f"- Used Thinking: {used}\n"
        
        return response + metadata_text
    
    def create_summary(
        self,
        response: str,
        max_length: int = 100,
    ) -> str:
        """
        Create a summary of the response.
        
        Args:
            response: Full response text
            max_length: Maximum summary length
            
        Returns:
            Response summary
        """
        # Simple extractive summary - take first sentence(s)
        sentences = re.split(r'[.!?]+', response)
        
        summary = ""
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence and len(summary + sentence) <= max_length:
                if summary:
                    summary += ". "
                summary += sentence
            else:
                break
        
        if summary and not summary.endswith(('.', '!', '?')):
            summary += "."
        
        return summary or response[:max_length] + "..."


class OutputValidator:
    """
    Validator for checking output quality and safety.
    """
    
    def __init__(self):
        # Safety patterns to check
        self.unsafe_patterns = [
            r"(?i)\b(kill|murder|suicide|bomb|weapon)\b",
            r"(?i)\b(hack|illegal|drugs|violence)\b",
        ]
        
        # Quality patterns
        self.quality_issues = [
            r"(.)\1{10,}",  # Excessive repetition
            r"(?i)\b(sorry|apologize)\b.*\b(sorry|apologize)\b",  # Over-apologizing
        ]
    
    def validate_output(
        self,
        text: str,
        check_safety: bool = True,
        check_quality: bool = True,
    ) -> Dict[str, Any]:
        """
        Validate output text.
        
        Args:
            text: Text to validate
            check_safety: Whether to check safety
            check_quality: Whether to check quality
            
        Returns:
            Validation results
        """
        results = {
            "is_safe": True,
            "is_high_quality": True,
            "issues": [],
            "warnings": [],
        }
        
        if check_safety:
            safety_results = self._check_safety(text)
            results["is_safe"] = safety_results["is_safe"]
            results["issues"].extend(safety_results["issues"])
        
        if check_quality:
            quality_results = self._check_quality(text)
            results["is_high_quality"] = quality_results["is_high_quality"]
            results["warnings"].extend(quality_results["warnings"])
        
        return results
    
    def _check_safety(self, text: str) -> Dict[str, Any]:
        """Check text safety."""
        issues = []
        
        for pattern in self.unsafe_patterns:
            if re.search(pattern, text):
                issues.append(f"Potentially unsafe content detected: {pattern}")
        
        return {
            "is_safe": len(issues) == 0,
            "issues": issues,
        }
    
    def _check_quality(self, text: str) -> Dict[str, Any]:
        """Check text quality."""
        warnings = []
        
        # Check for repetition
        for pattern in self.quality_issues:
            if re.search(pattern, text):
                warnings.append(f"Quality issue detected: {pattern}")
        
        # Check length
        if len(text.strip()) < 10:
            warnings.append("Response is very short")
        
        # Check for coherence (simple heuristic)
        sentences = re.split(r'[.!?]+', text)
        if len(sentences) > 5:
            # Check if sentences are too similar (simple check)
            unique_starts = set()
            for sentence in sentences[:5]:
                words = sentence.strip().split()
                if len(words) >= 3:
                    start = ' '.join(words[:3]).lower()
                    unique_starts.add(start)
            
            if len(unique_starts) < len(sentences[:5]) * 0.7:
                warnings.append("Potential coherence issues detected")
        
        return {
            "is_high_quality": len(warnings) == 0,
            "warnings": warnings,
        }
