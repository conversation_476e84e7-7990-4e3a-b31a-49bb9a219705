"""
CPU-safe training script for ThinkerLLM.
This version handles CUDA memory issues and provides fallback options.
"""
import sys
import os
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent.parent))
import torch
import argparse
import logging
import os
from pathlib import Path
from transformers import AutoTokenizer
import yaml

# Import ThinkerLLM components
from thinker_llm.models import IntegratedThinkerLLM
from thinker_llm.utils.config import ModelConfig, TrainingConfig
from thinker_llm.training.data_loader import prepare_dataset
from thinker_llm.training.trainer import ThinkerLLMTrainer
from thinker_llm.utils.model_utils import save_model, get_model_size_info

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def check_system_resources():
    """Check system resources and recommend settings."""
    logger.info("Checking system resources...")
    
    # Check CUDA
    cuda_available = torch.cuda.is_available()
    if cuda_available:
        try:
            device_name = torch.cuda.get_device_name()
            total_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
            logger.info(f"CUDA Device: {device_name}")
            logger.info(f"GPU Memory: {total_memory:.1f} GB")
            
            # Test CUDA operations
            test_tensor = torch.randn(100, 100)
            if torch.cuda.is_available():
                test_tensor = test_tensor.cuda()
                result = torch.mm(test_tensor, test_tensor)
                del test_tensor, result
                torch.cuda.empty_cache()
                logger.info("CUDA test: PASSED")
                return "cuda", total_memory
        except Exception as e:
            logger.warning(f"CUDA test failed: {e}")
            logger.info("Falling back to CPU")
            return "cpu", 0
    else:
        logger.info("CUDA not available, using CPU")
        return "cpu", 0


def get_safe_model_config(device_type: str, gpu_memory: float = 0) -> ModelConfig:
    """Get a safe model configuration based on available resources."""
    
    if device_type == "cuda" and gpu_memory > 8:
        # Large GPU
        logger.info("Using medium model configuration for large GPU")
        return ModelConfig(
            vocab_size=50257,
            hidden_size=512,
            num_layers=8,
            num_attention_heads=8,
            thinker_hidden_size=768,
            thinker_num_layers=6,
            use_flash_attention=False,  # Disable to avoid compatibility issues
            gradient_checkpointing=True,
        )
    elif device_type == "cuda" and gpu_memory > 4:
        # Medium GPU
        logger.info("Using small model configuration for medium GPU")
        return ModelConfig(
            vocab_size=50257,
            hidden_size=384,
            num_layers=6,
            num_attention_heads=6,
            thinker_hidden_size=512,
            thinker_num_layers=4,
            use_flash_attention=False,
            gradient_checkpointing=True,
        )
    else:
        # CPU or small GPU
        logger.info("Using tiny model configuration for CPU/small GPU")
        return ModelConfig(
            vocab_size=50257,
            hidden_size=256,
            num_layers=4,
            num_attention_heads=4,
            thinker_hidden_size=384,
            thinker_num_layers=3,
            use_flash_attention=False,
            gradient_checkpointing=False,  # Can cause issues on CPU
        )


def get_safe_training_config(device_type: str, gpu_memory: float = 0) -> TrainingConfig:
    """Get a safe training configuration based on available resources."""
    
    if device_type == "cuda" and gpu_memory > 8:
        batch_size = 4
        grad_accum = 2
    elif device_type == "cuda" and gpu_memory > 4:
        batch_size = 2
        grad_accum = 4
    else:
        batch_size = 1
        grad_accum = 8
    
    return TrainingConfig(
        batch_size=batch_size,
        gradient_accumulation_steps=grad_accum,
        learning_rate=3e-5,
        num_epochs=1,  # Start with 1 epoch for testing
        warmup_steps=50,
        max_length=512,  # Shorter sequences
        thinking_ratio=0.3,
        output_dir="./safe_outputs",
        logging_dir="./safe_logs",
        logging_steps=10,
        eval_steps=50,
        save_steps=100,
        max_grad_norm=1.0,
    )


def create_sample_data():
    """Create minimal sample data for testing."""
    logger.info("Creating sample training data...")
    
    samples = [
        {
            "instruction": "What is 2+2?",
            "thinking": "",
            "output": "2+2 equals 4.",
            "requires_thinking": False,
        },
        {
            "instruction": "What is the capital of France?",
            "thinking": "",
            "output": "The capital of France is Paris.",
            "requires_thinking": False,
        },
        {
            "instruction": "If I have 10 apples and eat 3, how many do I have left?",
            "thinking": "Let me calculate this step by step.\nStarting: 10 apples\nEaten: 3 apples\nRemaining: 10 - 3 = 7 apples",
            "output": "You would have 7 apples left. Starting with 10 and eating 3 leaves you with 7.",
            "requires_thinking": True,
        },
        {
            "instruction": "Explain why we need sleep.",
            "thinking": "This requires explaining the biological importance of sleep.\nKey points:\n1. Physical restoration\n2. Memory consolidation\n3. Immune system support\n4. Mental health",
            "output": "We need sleep for physical restoration, memory consolidation, immune system support, and mental health. During sleep, our bodies repair tissues and our brains process information from the day.",
            "requires_thinking": True,
        },
    ]
    
    # Create more samples by repeating
    extended_samples = []
    for i in range(10):  # 40 total samples
        for sample in samples:
            new_sample = sample.copy()
            if i > 0:
                new_sample["instruction"] = f"[V{i}] {sample['instruction']}"
            extended_samples.append(new_sample)
    
    # Create data directory
    os.makedirs("./safe_data", exist_ok=True)
    
    # Save data
    import json
    with open("./safe_data/train.jsonl", "w") as f:
        for sample in extended_samples:
            f.write(json.dumps(sample) + "\n")
    
    logger.info(f"Created {len(extended_samples)} training samples")
    return len(extended_samples)


def main():
    parser = argparse.ArgumentParser(description="Safe ThinkerLLM Training")
    parser.add_argument("--train-data", help="Path to training data (optional, will create sample data if not provided)")
    parser.add_argument("--force-cpu", action="store_true", help="Force CPU usage")
    parser.add_argument("--dry-run", action="store_true", help="Setup only, no training")
    parser.add_argument("--batch-size", type=int, help="Override batch size")
    parser.add_argument("--epochs", type=int, default=1, help="Number of epochs")
    
    args = parser.parse_args()
    
    # Check system resources
    if args.force_cpu:
        device_type, gpu_memory = "cpu", 0
        logger.info("Forced CPU usage")
    else:
        device_type, gpu_memory = check_system_resources()
    
    # Get safe configurations
    model_config = get_safe_model_config(device_type, gpu_memory)
    training_config = get_safe_training_config(device_type, gpu_memory)
    
    # Override settings if specified
    if args.batch_size:
        training_config.batch_size = args.batch_size
    training_config.num_epochs = args.epochs
    
    logger.info(f"Configuration:")
    logger.info(f"  - Device: {device_type}")
    logger.info(f"  - Model size: {model_config.hidden_size}d, {model_config.num_layers} layers")
    logger.info(f"  - Batch size: {training_config.batch_size}")
    logger.info(f"  - Gradient accumulation: {training_config.gradient_accumulation_steps}")
    logger.info(f"  - Epochs: {training_config.num_epochs}")
    
    # Prepare data
    if args.train_data and Path(args.train_data).exists():
        train_data_path = args.train_data
        logger.info(f"Using provided training data: {train_data_path}")
    else:
        create_sample_data()
        train_data_path = "./safe_data/train.jsonl"
        logger.info("Using generated sample data")
    
    # Setup model and tokenizer
    logger.info("Setting up model and tokenizer...")
    
    try:
        # Load tokenizer
        tokenizer = AutoTokenizer.from_pretrained("gpt2")
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        # Update vocab size
        model_config.vocab_size = len(tokenizer)
        
        # Create model
        model = IntegratedThinkerLLM(model_config)
        
        # Move to appropriate device
        if device_type == "cuda":
            model = model.cuda()
            logger.info("Model moved to CUDA")
        else:
            logger.info("Model on CPU")
        
        # Print model info
        model_info = get_model_size_info(model)
        logger.info(f"Model created successfully:")
        logger.info(f"  - Parameters: {model_info['total_parameters']:,}")
        logger.info(f"  - Size: {model_info['model_size_mb']:.1f} MB")
        
    except Exception as e:
        logger.error(f"Failed to create model: {e}")
        logger.info("Trying even smaller configuration...")
        
        # Ultra-small fallback
        model_config.hidden_size = 128
        model_config.num_layers = 2
        model_config.thinker_hidden_size = 192
        model_config.thinker_num_layers = 2
        
        model = IntegratedThinkerLLM(model_config)
        if device_type == "cuda":
            model = model.cuda()
        
        logger.info("Created ultra-small model as fallback")
    
    if args.dry_run:
        logger.info("Dry run completed successfully!")
        return
    
    # Prepare dataset
    logger.info("Preparing dataset...")
    try:
        train_dataset, train_dataloader = prepare_dataset(
            data_path=train_data_path,
            tokenizer_name="gpt2",
            max_length=training_config.max_length,
            thinking_ratio=training_config.thinking_ratio,
            batch_size=training_config.batch_size,
            split="train",
        )
        logger.info(f"Dataset prepared: {len(train_dataset)} samples")
    except Exception as e:
        logger.error(f"Failed to prepare dataset: {e}")
        return
    
    # Create trainer
    logger.info("Setting up trainer...")
    try:
        trainer = ThinkerLLMTrainer(
            model=model,
            train_dataloader=train_dataloader.get_dataloader(),
            config=training_config,
            model_config=model_config,
        )
        logger.info("Trainer created successfully")
    except Exception as e:
        logger.error(f"Failed to create trainer: {e}")
        return
    
    # Start training
    logger.info("Starting training...")
    try:
        trainer.train()
        logger.info("Training completed successfully!")
        
        # Save model
        model_save_path = "./safe_outputs/safe_thinker_llm"
        save_model(
            model=model,
            tokenizer=tokenizer,
            save_path=model_save_path,
            model_config=model_config,
            training_config=training_config,
            additional_info={
                "device_type": device_type,
                "gpu_memory": gpu_memory,
                "safe_training": True,
            }
        )
        logger.info(f"Model saved to {model_save_path}")
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        logger.info("This might be due to insufficient memory or CUDA issues")
        logger.info("Try running with --force-cpu or reducing batch size")
        raise


if __name__ == "__main__":
    main()
