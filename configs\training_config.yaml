# Training configuration for ThinkerLLM
# Optimized for getting started with your own data

# Model Architecture Configuration
model:
  # Main LLM Configuration (REDUCED for memory safety)
  vocab_size: 50257  # GPT-2 vocabulary size (will be auto-adjusted)
  hidden_size: 256   # Reduced for memory safety
  num_layers: 4      # Reduced for memory safety
  num_attention_heads: 4
  intermediate_size: 1024
  max_position_embeddings: 512  # Shorter sequences for efficiency
  dropout_prob: 0.1
  layer_norm_eps: 1.0e-5

  # ThinkerModule Configuration (REDUCED for memory safety)
  thinker_hidden_size: 384
  thinker_num_layers: 3
  thinker_num_heads: 6
  thinker_intermediate_size: 1536
  thinker_max_length: 256
  thinker_dropout: 0.1

  # Decision Mechanism Configuration
  decision_hidden_size: 256
  decision_num_layers: 3
  decision_threshold: 0.5
  decision_features:
    - "input_length"
    - "complexity_score"
    - "question_type"

  # Projection Layer Configuration
  projection_type: "adaptive"  # "linear", "adaptive", "cross_attention"
  projection_hidden_size: 256
  projection_num_layers: 2

  # General Configuration
  use_flash_attention: false  # Set to true if you have CUDA and flash-attn installed
  gradient_checkpointing: false  # Save memory
  tie_word_embeddings: true

# Training Configuration
training:
  # Training Parameters (REDUCED for memory safety)
  batch_size: 1      # Reduced for memory safety
  learning_rate: 5.0e-5
  num_epochs: 1      # Start with 1 epoch for testing
  warmup_steps: 50
  max_grad_norm: 1.0
  weight_decay: 0.01

  # Loss Weights (adjust based on your priorities)
  llm_loss_weight: 1.0
  thinker_loss_weight: 0.6    # Slightly higher for thinking capability
  decision_loss_weight: 0.4

  # Training Strategy
  joint_training: true
  pretrain_components: false
  freeze_llm: false
  freeze_thinker: false

  # Optimization
  optimizer: "adamw"
  scheduler: "cosine"
  gradient_accumulation_steps: 2  # Effective batch size = batch_size * this

  # Mixed Precision Training (NEW)
  use_amp: true  # Enable automatic mixed precision for faster training
  mixed_precision_dtype: "bf16"  # "fp16" or "bf16" - bf16 is more stable

  # Logging and Checkpointing
  logging_steps: 50
  eval_steps: 200
  save_steps: 500
  save_total_limit: 3

  # Data
  max_length: 256
  thinking_ratio: 0.35  # 35% of samples require thinking

  # Paths (will be overridden by command line arguments)
  output_dir: "./outputs"
  logging_dir: "./logs"
  cache_dir: "./cache"

# Inference Configuration
inference:
  # Generation Parameters
  max_new_tokens: 256
  temperature: 0.7
  top_p: 0.9
  top_k: 50
  do_sample: true
  num_beams: 1

  # Thinking Parameters
  show_thinking: true
  thinking_temperature: 0.8
  max_thinking_tokens: 200

  # Decision Parameters
  decision_threshold: 0.5
  force_thinking: false
  disable_thinking: false

  # Performance
  batch_size: 1
  use_cache: true

  # Output Formatting
  include_metadata: true
  format_thoughts: true
  clean_output: true

# Data Configuration
data:
  # Training data paths (will be set by command line)
  train_data_path: "./data/train.jsonl"
  val_data_path: "./data/val.jsonl"
  test_data_path: "./data/test.jsonl"

  # Tokenizer
  tokenizer_name: "gpt2"

  # Data processing
  max_length: 1024
  thinking_ratio: 0.35
  include_thoughts: true

  # Data loader
  num_workers: 2  # Reduce if you have memory issues
  pin_memory: true
  shuffle_train: true

# Environment Configuration
environment:
  # Device settings
  device: "auto"  # Will auto-detect CUDA/CPU
  mixed_precision: true   # UPDATED: Enable mixed precision for faster training
  compile_model: false    # PyTorch 2.0+ compilation (experimental)

  # Memory optimization
  gradient_checkpointing: true
  use_flash_attention: false  # Enable if you have flash-attn installed

  # Reproducibility
  seed: 42
  deterministic: false

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

  # Weights & Biases (set to false if you don't have wandb)
  use_wandb: false
  wandb_project: "thinker-llm"
  wandb_entity: null

  # TensorBoard
  use_tensorboard: true

  # Console logging
  log_to_console: true
  log_to_file: true

# Model Variants for Different Use Cases
variants:
  # Development/Testing - Very small and fast
  dev:
    model:
      hidden_size: 256
      num_layers: 4
      num_attention_heads: 4
      thinker_hidden_size: 384
      thinker_num_layers: 3
    training:
      batch_size: 8
      learning_rate: 1.0e-4
      num_epochs: 1

  # Small - Good for limited resources
  small:
    model:
      hidden_size: 384
      num_layers: 6
      num_attention_heads: 6
      thinker_hidden_size: 512
      thinker_num_layers: 4
    training:
      batch_size: 6
      learning_rate: 5.0e-5
      num_epochs: 2

  # Medium - Balanced performance and resources
  medium:
    model:
      hidden_size: 768
      num_layers: 12
      num_attention_heads: 12
      thinker_hidden_size: 1024
      thinker_num_layers: 8
    training:
      batch_size: 4
      learning_rate: 3.0e-5
      num_epochs: 3

  # Large - Best performance, requires significant resources
  large:
    model:
      hidden_size: 1024
      num_layers: 24
      num_attention_heads: 16
      thinker_hidden_size: 1536
      thinker_num_layers: 12
    training:
      batch_size: 2
      learning_rate: 2.0e-5
      num_epochs: 5
      gradient_accumulation_steps: 4
