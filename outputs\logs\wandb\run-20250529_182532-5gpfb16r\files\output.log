INFO:__main__:Starting training...
INFO:thinker_llm.training.trainer:Starting training...
INFO:thinker_llm.training.trainer:Total epochs: 1
INFO:thinker_llm.training.trainer:Total steps: 40
Epoch 1/1:   0%|                                                | 0/40 [00:00<?, ?it/s, loss=4.3892, lr=0.00e+00]INFO:thinker_llm.training.trainer:train/loss: 4.3892
INFO:thinker_llm.training.trainer:train/learning_rate: 0.0000
INFO:thinker_llm.training.trainer:train/global_step: 0.0000
INFO:thinker_llm.training.trainer:Checkpoint saved: ./outputs\checkpoint_step_0.pt
INFO:thinker_llm.training.trainer:train/epoch_loss: 4.2520                                                       
INFO:thinker_llm.training.trainer:train/epoch_llm_lm_loss: 8.2165
INFO:thinker_llm.training.trainer:train/epoch_llm_total_loss: 8.2165
INFO:thinker_llm.training.trainer:train/epoch_decision_decision_loss: 0.6931
INFO:thinker_llm.training.trainer:train/epoch_decision_calibration_loss: 0.0250
INFO:thinker_llm.training.trainer:train/epoch_decision_efficiency_loss: 0.0900
INFO:thinker_llm.training.trainer:train/epoch_decision_total_loss: 0.7186
INFO:thinker_llm.training.trainer:train/epoch_total_loss: 8.5039
INFO:thinker_llm.training.trainer:Epoch 1/1 completed. Train loss: 4.2520
INFO:thinker_llm.training.trainer:Training completed!
INFO:thinker_llm.training.trainer:Checkpoint saved: ./outputs\final_checkpoint.pt
INFO:__main__:Training completed successfully!
INFO:__main__:Saving model to ./outputs\thinker_llm
INFO:root:Model saved to outputs\thinker_llm
INFO:__main__:Model saved successfully!
