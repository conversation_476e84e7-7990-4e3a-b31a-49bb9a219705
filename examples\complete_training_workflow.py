"""
Complete training workflow example for ThinkerLLM.

This script demonstrates the entire process from data preparation to model training and testing.
"""

import os
import json
import logging
from pathlib import Path
from transformers import AutoTokenizer

# Import ThinkerLLM components
from thinker_llm.models import IntegratedThinkerLLM
from thinker_llm.utils.config import ModelConfig, TrainingConfig
from thinker_llm.training.data_loader import prepare_dataset
from thinker_llm.training.trainer import Thinker<PERSON><PERSON>rainer
from thinker_llm.inference.pipeline import Thinker<PERSON><PERSON>ipeline
from thinker_llm.inference.config import InferenceConfig
from thinker_llm.utils.model_utils import save_model, load_model

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_sample_dataset():
    """Create a sample dataset for demonstration."""
    logger.info("Creating sample dataset...")
    
    # Sample training data with various complexity levels
    training_samples = [
        # Simple factual questions (no thinking needed)
        {
            "instruction": "What is the capital of France?",
            "thinking": "",
            "output": "The capital of France is Paris.",
            "requires_thinking": False,
        },
        {
            "instruction": "What color is grass?",
            "thinking": "",
            "output": "Grass is typically green.",
            "requires_thinking": False,
        },
        {
            "instruction": "How many days are in a week?",
            "thinking": "",
            "output": "There are 7 days in a week.",
            "requires_thinking": False,
        },
        
        # Mathematical problems (thinking needed)
        {
            "instruction": "If I have 12 apples and eat 3, then buy 8 more, how many apples do I have?",
            "thinking": "Let me work through this step by step.\n\nStarting amount: 12 apples\nEat: 3 apples\nAfter eating: 12 - 3 = 9 apples\nBuy more: 8 apples\nFinal amount: 9 + 8 = 17 apples",
            "output": "You would have 17 apples. Starting with 12, after eating 3 you'd have 9, then adding 8 more gives you 17 total.",
            "requires_thinking": True,
        },
        {
            "instruction": "What is 15% of 80?",
            "thinking": "I need to calculate 15% of 80.\n\n15% = 15/100 = 0.15\n15% of 80 = 0.15 × 80 = 12",
            "output": "15% of 80 is 12.",
            "requires_thinking": True,
        },
        
        # Reasoning tasks (thinking needed)
        {
            "instruction": "Why do we need to sleep?",
            "thinking": "This is a complex question about human biology. Let me think about the main reasons:\n\n1. Physical restoration - body repairs tissues\n2. Mental restoration - brain processes information\n3. Memory consolidation - important memories are strengthened\n4. Immune system support - sleep helps fight illness\n5. Hormone regulation - growth and stress hormones are balanced",
            "output": "We need sleep for several important reasons: physical restoration (tissue repair), mental restoration (brain processing), memory consolidation, immune system support, and hormone regulation. Sleep is essential for both physical and mental health.",
            "requires_thinking": True,
        },
        {
            "instruction": "What are the advantages of renewable energy?",
            "thinking": "I should consider the main benefits of renewable energy sources:\n\n1. Environmental benefits - reduced pollution and greenhouse gases\n2. Sustainability - sources like solar and wind are inexhaustible\n3. Economic benefits - job creation and energy independence\n4. Health benefits - cleaner air and water\n5. Long-term cost savings - lower operating costs after initial investment",
            "output": "Renewable energy offers several key advantages: environmental protection through reduced pollution, sustainability as sources are inexhaustible, economic benefits including job creation and energy independence, health improvements from cleaner air and water, and long-term cost savings despite higher initial investments.",
            "requires_thinking": True,
        },
        
        # Creative tasks (thinking needed)
        {
            "instruction": "Write a short poem about the ocean.",
            "thinking": "I need to create a short poem about the ocean. Let me think about key imagery and themes:\n- Waves and movement\n- Vastness and depth\n- Marine life\n- Colors (blue, green)\n- Sounds (crashing, whispers)\n\nI'll aim for a simple but evocative style.",
            "output": "Endless blue stretches far and wide,\nWaves dance with the changing tide.\nDeep beneath where secrets sleep,\nThe ocean's mysteries run deep.\nWhispers of salt upon the breeze,\nNature's song across the seas.",
            "requires_thinking": True,
        },
    ]
    
    # Create more samples by repeating with variations
    extended_samples = []
    for i in range(20):  # Create 20 iterations
        for sample in training_samples:
            # Add some variation to avoid exact repetition
            new_sample = sample.copy()
            if i > 0:
                new_sample["instruction"] = f"[Variation {i}] {sample['instruction']}"
            extended_samples.append(new_sample)
    
    # Create train/val split
    train_samples = extended_samples[:int(len(extended_samples) * 0.8)]
    val_samples = extended_samples[int(len(extended_samples) * 0.8):]
    
    # Create data directory
    os.makedirs("./demo_data", exist_ok=True)
    
    # Save training data
    with open("./demo_data/train.jsonl", "w") as f:
        for sample in train_samples:
            f.write(json.dumps(sample) + "\n")
    
    # Save validation data
    with open("./demo_data/val.jsonl", "w") as f:
        for sample in val_samples:
            f.write(json.dumps(sample) + "\n")
    
    logger.info(f"Created {len(train_samples)} training samples and {len(val_samples)} validation samples")
    return len(train_samples), len(val_samples)


def setup_training_config():
    """Setup training configuration for the demo."""
    logger.info("Setting up training configuration...")
    
    # Small model configuration for demo
    model_config = ModelConfig(
        vocab_size=50257,  # GPT-2 vocab size
        hidden_size=384,   # Small for demo
        num_layers=4,
        num_attention_heads=6,
        intermediate_size=1536,
        thinker_hidden_size=512,
        thinker_num_layers=3,
        thinker_num_heads=8,
        max_position_embeddings=1024,
        use_flash_attention=False,  # Disable for CPU compatibility
    )
    
    # Training configuration
    training_config = TrainingConfig(
        batch_size=2,      # Small batch for demo
        learning_rate=5e-5,
        num_epochs=2,      # Quick training for demo
        warmup_steps=50,
        max_length=512,
        thinking_ratio=0.4,  # 40% thinking samples
        output_dir="./demo_outputs",
        logging_dir="./demo_logs",
        logging_steps=10,
        eval_steps=20,
        save_steps=50,
    )
    
    return model_config, training_config


def train_model(model_config, training_config):
    """Train the ThinkerLLM model."""
    logger.info("Starting model training...")
    
    # Setup tokenizer
    tokenizer = AutoTokenizer.from_pretrained("gpt2")
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Create model
    model = IntegratedThinkerLLM(model_config)
    
    # Print model info
    model_info = model.get_model_info()
    logger.info(f"Model created with {model_info['total_parameters']:,} parameters")
    
    # Prepare datasets
    train_dataset, train_dataloader = prepare_dataset(
        data_path="./demo_data/train.jsonl",
        tokenizer_name="gpt2",
        max_length=training_config.max_length,
        thinking_ratio=training_config.thinking_ratio,
        batch_size=training_config.batch_size,
        split="train",
    )
    
    val_dataset, val_dataloader = prepare_dataset(
        data_path="./demo_data/val.jsonl",
        tokenizer_name="gpt2",
        max_length=training_config.max_length,
        thinking_ratio=training_config.thinking_ratio,
        batch_size=training_config.batch_size,
        split="train",
    )
    
    logger.info(f"Training dataset: {len(train_dataset)} samples")
    logger.info(f"Validation dataset: {len(val_dataset)} samples")
    
    # Create trainer
    trainer = ThinkerLLMTrainer(
        model=model,
        train_dataloader=train_dataloader.get_dataloader(),
        val_dataloader=val_dataloader.get_dataloader(),
        config=training_config,
        model_config=model_config,
    )
    
    # Train the model
    trainer.train()
    
    # Save the model
    model_save_path = "./demo_outputs/demo_thinker_llm"
    save_model(
        model=model,
        tokenizer=tokenizer,
        save_path=model_save_path,
        model_config=model_config,
        training_config=training_config,
        additional_info={
            "demo_training": True,
            "final_step": trainer.global_step,
            "best_val_loss": trainer.best_val_loss,
        }
    )
    
    logger.info(f"Model saved to {model_save_path}")
    return model_save_path


def test_trained_model(model_path):
    """Test the trained model."""
    logger.info("Testing trained model...")
    
    # Load the trained model
    loaded = load_model(model_path)
    model = loaded["model"]
    tokenizer = loaded["tokenizer"]
    
    # Create inference pipeline
    pipeline = ThinkerLLMPipeline(
        model=model,
        tokenizer=tokenizer,
        config=InferenceConfig(
            max_new_tokens=150,
            temperature=0.7,
            show_thinking=True,
        )
    )
    
    # Test prompts
    test_prompts = [
        "What is the capital of Spain?",  # Simple factual
        "If I have 20 dollars and spend 7, how much do I have left?",  # Math
        "Why is exercise important for health?",  # Reasoning
        "What are the benefits of reading books?",  # Analysis
    ]
    
    logger.info("Testing model responses...")
    print("\n" + "="*80)
    print("TRAINED MODEL TEST RESULTS")
    print("="*80)
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n{i}. Prompt: {prompt}")
        print("-" * 60)
        
        result = pipeline.generate(prompt, return_thoughts=True)
        
        print(f"Used Thinking: {result['used_thinking']}")
        print(f"Thinking Probability: {result['thinking_probability']:.3f}")
        
        if result.get('thoughts'):
            print(f"Thoughts: {result['thoughts'][:200]}...")
        
        print(f"Response: {result['response']}")
        print(f"Generation Time: {result['generation_time']:.2f}s")
    
    # Analyze decision mechanism
    print(f"\n{'='*80}")
    print("DECISION MECHANISM ANALYSIS")
    print("="*80)
    
    for prompt in test_prompts[:2]:  # Analyze first 2 prompts
        analysis = pipeline.analyze_decision(prompt)
        print(f"\nPrompt: {prompt}")
        print(f"Should Think: {analysis['should_think']}")
        print(f"Thinking Probability: {analysis['thinking_probability']:.3f}")
        print(f"Question Type: {analysis['question_type']}")


def main():
    """Main function for complete training workflow."""
    logger.info("Starting complete ThinkerLLM training workflow...")
    
    try:
        # Step 1: Create sample dataset
        train_size, val_size = create_sample_dataset()
        
        # Step 2: Setup configuration
        model_config, training_config = setup_training_config()
        
        # Step 3: Train model
        model_path = train_model(model_config, training_config)
        
        # Step 4: Test trained model
        test_trained_model(model_path)
        
        logger.info("Complete workflow finished successfully!")
        
        print(f"\n{'='*80}")
        print("WORKFLOW SUMMARY")
        print("="*80)
        print(f"✅ Created dataset: {train_size} train, {val_size} val samples")
        print(f"✅ Trained model: {model_path}")
        print(f"✅ Model tested successfully")
        print(f"\nYour trained model is ready to use!")
        print(f"Model location: {model_path}")
        
    except Exception as e:
        logger.error(f"Workflow failed: {e}")
        raise


if __name__ == "__main__":
    main()
