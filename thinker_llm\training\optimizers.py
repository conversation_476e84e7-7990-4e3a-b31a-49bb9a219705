"""
Optimizer and scheduler utilities for ThinkerLLM training.
"""

import torch
import torch.nn as nn
from torch.optim import <PERSON><PERSON>, <PERSON>, SGD
from torch.optim.lr_scheduler import (
    LinearLR, 
    CosineAnnealingLR, 
    StepLR, 
    ExponentialLR,
    ReduceLROnPlateau
)
from transformers import get_linear_schedule_with_warmup, get_cosine_schedule_with_warmup
from typing import Union, Dict, Any


def get_optimizer(
    model: nn.Module,
    optimizer_name: str = "adamw",
    learning_rate: float = 5e-5,
    weight_decay: float = 0.01,
    **kwargs
) -> torch.optim.Optimizer:
    """
    Get optimizer for model training.
    
    Args:
        model: Model to optimize
        optimizer_name: Name of optimizer ("adamw", "adam", "sgd")
        learning_rate: Learning rate
        weight_decay: Weight decay coefficient
        **kwargs: Additional optimizer arguments
        
    Returns:
        Configured optimizer
    """
    # Get model parameters with different learning rates for different components
    param_groups = get_parameter_groups(model, learning_rate, weight_decay)
    
    if optimizer_name.lower() == "adamw":
        optimizer = AdamW(
            param_groups,
            lr=learning_rate,
            weight_decay=weight_decay,
            betas=kwargs.get("betas", (0.9, 0.999)),
            eps=kwargs.get("eps", 1e-8),
        )
    elif optimizer_name.lower() == "adam":
        optimizer = Adam(
            param_groups,
            lr=learning_rate,
            weight_decay=weight_decay,
            betas=kwargs.get("betas", (0.9, 0.999)),
            eps=kwargs.get("eps", 1e-8),
        )
    elif optimizer_name.lower() == "sgd":
        optimizer = SGD(
            param_groups,
            lr=learning_rate,
            weight_decay=weight_decay,
            momentum=kwargs.get("momentum", 0.9),
            nesterov=kwargs.get("nesterov", True),
        )
    else:
        raise ValueError(f"Unknown optimizer: {optimizer_name}")
    
    return optimizer


def get_parameter_groups(
    model: nn.Module,
    learning_rate: float,
    weight_decay: float,
) -> list:
    """
    Create parameter groups with different learning rates for different components.
    
    Args:
        model: Model to get parameters from
        learning_rate: Base learning rate
        weight_decay: Weight decay coefficient
        
    Returns:
        List of parameter groups
    """
    # Different learning rates for different components
    lr_multipliers = {
        "decision_mechanism": 1.0,  # Standard LR for decision mechanism
        "thinker_module": 0.8,     # Slightly lower LR for thinker
        "main_llm": 0.5,           # Lower LR for main LLM (if pre-trained)
    }
    
    param_groups = []
    
    # Group parameters by component
    for component_name, lr_mult in lr_multipliers.items():
        if hasattr(model, component_name):
            component = getattr(model, component_name)
            
            # Separate parameters with and without weight decay
            decay_params = []
            no_decay_params = []
            
            for name, param in component.named_parameters():
                if param.requires_grad:
                    # No weight decay for bias and layer norm parameters
                    if "bias" in name or "layer_norm" in name or "ln" in name:
                        no_decay_params.append(param)
                    else:
                        decay_params.append(param)
            
            # Add parameter groups
            if decay_params:
                param_groups.append({
                    "params": decay_params,
                    "lr": learning_rate * lr_mult,
                    "weight_decay": weight_decay,
                    "component": component_name,
                })
            
            if no_decay_params:
                param_groups.append({
                    "params": no_decay_params,
                    "lr": learning_rate * lr_mult,
                    "weight_decay": 0.0,
                    "component": f"{component_name}_no_decay",
                })
    
    # If no specific components found, use all parameters
    if not param_groups:
        decay_params = []
        no_decay_params = []
        
        for name, param in model.named_parameters():
            if param.requires_grad:
                if "bias" in name or "layer_norm" in name or "ln" in name:
                    no_decay_params.append(param)
                else:
                    decay_params.append(param)
        
        if decay_params:
            param_groups.append({
                "params": decay_params,
                "lr": learning_rate,
                "weight_decay": weight_decay,
            })
        
        if no_decay_params:
            param_groups.append({
                "params": no_decay_params,
                "lr": learning_rate,
                "weight_decay": 0.0,
            })
    
    return param_groups


def get_scheduler(
    optimizer: torch.optim.Optimizer,
    scheduler_name: str = "cosine",
    num_training_steps: int = 1000,
    warmup_steps: int = 100,
    **kwargs
) -> Union[torch.optim.lr_scheduler._LRScheduler, object]:
    """
    Get learning rate scheduler.
    
    Args:
        optimizer: Optimizer to schedule
        scheduler_name: Name of scheduler
        num_training_steps: Total number of training steps
        warmup_steps: Number of warmup steps
        **kwargs: Additional scheduler arguments
        
    Returns:
        Configured scheduler
    """
    if scheduler_name.lower() == "linear":
        scheduler = get_linear_schedule_with_warmup(
            optimizer=optimizer,
            num_warmup_steps=warmup_steps,
            num_training_steps=num_training_steps,
        )
    elif scheduler_name.lower() == "cosine":
        scheduler = get_cosine_schedule_with_warmup(
            optimizer=optimizer,
            num_warmup_steps=warmup_steps,
            num_training_steps=num_training_steps,
            num_cycles=kwargs.get("num_cycles", 0.5),
        )
    elif scheduler_name.lower() == "cosine_annealing":
        scheduler = CosineAnnealingLR(
            optimizer=optimizer,
            T_max=num_training_steps - warmup_steps,
            eta_min=kwargs.get("eta_min", 0),
        )
    elif scheduler_name.lower() == "step":
        scheduler = StepLR(
            optimizer=optimizer,
            step_size=kwargs.get("step_size", num_training_steps // 3),
            gamma=kwargs.get("gamma", 0.1),
        )
    elif scheduler_name.lower() == "exponential":
        scheduler = ExponentialLR(
            optimizer=optimizer,
            gamma=kwargs.get("gamma", 0.95),
        )
    elif scheduler_name.lower() == "plateau":
        scheduler = ReduceLROnPlateau(
            optimizer=optimizer,
            mode=kwargs.get("mode", "min"),
            factor=kwargs.get("factor", 0.5),
            patience=kwargs.get("patience", 10),
            verbose=kwargs.get("verbose", True),
        )
    elif scheduler_name.lower() == "none":
        # No scheduler
        class NoScheduler:
            def step(self):
                pass
            def get_last_lr(self):
                return [group['lr'] for group in optimizer.param_groups]
            def state_dict(self):
                return {}
            def load_state_dict(self, state_dict):
                pass
        
        scheduler = NoScheduler()
    else:
        raise ValueError(f"Unknown scheduler: {scheduler_name}")
    
    return scheduler


class WarmupScheduler:
    """
    Custom warmup scheduler that can be combined with other schedulers.
    """
    
    def __init__(
        self,
        optimizer: torch.optim.Optimizer,
        warmup_steps: int,
        base_scheduler: torch.optim.lr_scheduler._LRScheduler = None,
    ):
        self.optimizer = optimizer
        self.warmup_steps = warmup_steps
        self.base_scheduler = base_scheduler
        self.step_count = 0
        
        # Store initial learning rates
        self.base_lrs = [group['lr'] for group in optimizer.param_groups]
    
    def step(self):
        """Step the scheduler."""
        self.step_count += 1
        
        if self.step_count <= self.warmup_steps:
            # Warmup phase
            warmup_factor = self.step_count / self.warmup_steps
            for i, group in enumerate(self.optimizer.param_groups):
                group['lr'] = self.base_lrs[i] * warmup_factor
        else:
            # Use base scheduler if provided
            if self.base_scheduler is not None:
                self.base_scheduler.step()
    
    def get_last_lr(self):
        """Get last learning rates."""
        return [group['lr'] for group in self.optimizer.param_groups]
    
    def state_dict(self):
        """Get scheduler state."""
        state = {
            'step_count': self.step_count,
            'base_lrs': self.base_lrs,
        }
        if self.base_scheduler is not None:
            state['base_scheduler'] = self.base_scheduler.state_dict()
        return state
    
    def load_state_dict(self, state_dict):
        """Load scheduler state."""
        self.step_count = state_dict['step_count']
        self.base_lrs = state_dict['base_lrs']
        if self.base_scheduler is not None and 'base_scheduler' in state_dict:
            self.base_scheduler.load_state_dict(state_dict['base_scheduler'])


def create_custom_scheduler(
    optimizer: torch.optim.Optimizer,
    warmup_steps: int,
    total_steps: int,
    schedule_type: str = "cosine",
    **kwargs
) -> WarmupScheduler:
    """
    Create a custom scheduler with warmup.
    
    Args:
        optimizer: Optimizer to schedule
        warmup_steps: Number of warmup steps
        total_steps: Total training steps
        schedule_type: Type of schedule after warmup
        **kwargs: Additional arguments for base scheduler
        
    Returns:
        Custom warmup scheduler
    """
    # Create base scheduler for after warmup
    if schedule_type == "cosine":
        base_scheduler = CosineAnnealingLR(
            optimizer=optimizer,
            T_max=total_steps - warmup_steps,
            eta_min=kwargs.get("eta_min", 0),
        )
    elif schedule_type == "linear":
        base_scheduler = LinearLR(
            optimizer=optimizer,
            start_factor=1.0,
            end_factor=kwargs.get("end_factor", 0.0),
            total_iters=total_steps - warmup_steps,
        )
    elif schedule_type == "constant":
        base_scheduler = None  # Keep constant after warmup
    else:
        raise ValueError(f"Unknown schedule type: {schedule_type}")
    
    return WarmupScheduler(
        optimizer=optimizer,
        warmup_steps=warmup_steps,
        base_scheduler=base_scheduler,
    )
