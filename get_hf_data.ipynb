{"cells": [{"cell_type": "code", "execution_count": 3, "id": "11b6b141", "metadata": {}, "outputs": [], "source": ["from datasets import load_dataset\n", "import json\n", "from tqdm import tqdm"]}, {"cell_type": "code", "execution_count": 2, "id": "1955b3f3", "metadata": {}, "outputs": [], "source": ["# Load dataset in streaming mode\n", "dataset = load_dataset(\"KingNish/reasoning-base-20k\", streaming=True)"]}, {"cell_type": "code", "execution_count": 4, "id": "4720e999", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["19944it [04:20, 76.44it/s]  \n"]}], "source": ["# Open output file\n", "with open(\"test_data/train.jsonl\", \"w\", encoding=\"utf-8\") as f:\n", "    # Iterate through dataset\n", "    for record in tqdm(dataset[\"train\"]):\n", "        # Write each record as a JSON line\n", "        data = {\n", "            \"instruction\": record[\"user\"],\n", "            \"thinking\": record[\"reasoning\"],\n", "            \"output\": record[\"assistant\"],\n", "            \"requires_thinking\": True,\n", "        }\n", "        json.dump(data, f, ensure_ascii=False)\n", "        f.write(\"\\n\")"]}], "metadata": {"kernelspec": {"display_name": "tts", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}