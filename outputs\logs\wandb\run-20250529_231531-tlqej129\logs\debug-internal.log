{"time":"2025-05-29T23:15:31.1304709+07:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"outputs\\logs\\wandb\\run-20250529_231531-tlqej129\\logs\\debug-core.log"}
{"time":"2025-05-29T23:15:31.2371163+07:00","level":"INFO","msg":"created new stream","id":"tlqej129"}
{"time":"2025-05-29T23:15:31.2371163+07:00","level":"INFO","msg":"stream: started","id":"tlqej129"}
{"time":"2025-05-29T23:15:31.2371163+07:00","level":"INFO","msg":"handler: started","stream_id":"tlqej129"}
{"time":"2025-05-29T23:15:31.2371163+07:00","level":"INFO","msg":"writer: Do: started","stream_id":"tlqej129"}
{"time":"2025-05-29T23:15:31.2371163+07:00","level":"INFO","msg":"sender: started","stream_id":"tlqej129"}
{"time":"2025-05-29T23:15:31.6213207+07:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-05-29T23:15:46.6400759+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:16:01.6333499+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:16:16.6328536+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:16:31.6406006+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:16:46.6383136+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:17:01.6456269+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:17:16.6382767+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:17:31.6457848+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:17:46.6322049+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:18:01.6404662+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:18:16.6346067+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:18:31.6385489+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:18:46.6679088+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:19:01.6391486+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:19:16.6420016+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:19:31.6433369+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:19:46.6401341+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:20:01.6416046+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:20:16.6469409+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:20:31.6404435+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:20:46.6381852+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:21:01.6352252+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:21:16.6453611+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:21:31.6430969+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:21:46.6371008+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:21:58.2656881+07:00","level":"INFO","msg":"stream: closing","id":"tlqej129"}
{"time":"2025-05-29T23:21:58.2656881+07:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-05-29T23:21:58.2661905+07:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-05-29T23:21:59.9047744+07:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-05-29T23:22:00.2128814+07:00","level":"INFO","msg":"handler: closed","stream_id":"tlqej129"}
{"time":"2025-05-29T23:22:00.2128814+07:00","level":"INFO","msg":"sender: closed","stream_id":"tlqej129"}
{"time":"2025-05-29T23:22:00.2128814+07:00","level":"INFO","msg":"writer: Close: closed","stream_id":"tlqej129"}
{"time":"2025-05-29T23:22:00.2128814+07:00","level":"INFO","msg":"stream: closed","id":"tlqej129"}
