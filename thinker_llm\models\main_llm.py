"""
MainLLM: Traditional autoregressive language model component.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple, Dict, Any, Union
import math

from .attention import CausalSelfAttention
from .projection_layers import ProjectionLayer, AdaptiveProjection, CrossAttentionProjection
from ..utils.config import ModelConfig


class LLMBlock(nn.Module):
    """
    Standard transformer block for autoregressive language modeling.
    """

    def __init__(
        self,
        hidden_size: int,
        num_heads: int,
        intermediate_size: int,
        dropout: float = 0.1,
        use_flash_attention: bool = True,
    ):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_heads = num_heads
        self.intermediate_size = intermediate_size

        # Self-attention layer
        self.self_attention = CausalSelfAttention(
            hidden_size=hidden_size,
            num_heads=num_heads,
            dropout=dropout,
            use_flash_attention=use_flash_attention,
        )

        # Feed-forward network
        self.ffn = nn.Sequential(
            nn.Linear(hidden_size, intermediate_size),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(intermediate_size, hidden_size),
            nn.Dropout(dropout),
        )

        # Layer normalization
        self.ln1 = nn.LayerNorm(hidden_size)
        self.ln2 = nn.LayerNorm(hidden_size)

    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        position_embeddings: Optional[torch.Tensor] = None,
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass through LLMBlock.

        Args:
            hidden_states: Input tensor (batch_size, seq_len, hidden_size)
            attention_mask: Optional attention mask
            position_embeddings: Optional position embeddings

        Returns:
            Tuple of (output_states, attention_weights)
        """
        # Self-attention with residual connection
        attn_output, attn_weights = self.self_attention(
            hidden_states=hidden_states,
            attention_mask=attention_mask,
            position_embeddings=position_embeddings,
        )
        hidden_states = self.ln1(hidden_states + attn_output)

        # Feed-forward with residual connection
        ffn_output = self.ffn(hidden_states)
        hidden_states = self.ln2(hidden_states + ffn_output)

        return hidden_states, attn_weights


class MainLLM(nn.Module):
    """
    Traditional autoregressive language model that can optionally
    integrate reasoning states from ThinkerModule.
    """

    def __init__(self, config: ModelConfig):
        super().__init__()
        self.config = config
        self.hidden_size = config.hidden_size
        self.num_layers = config.num_layers
        self.num_heads = config.num_attention_heads
        self.vocab_size = config.vocab_size
        self.max_position_embeddings = config.max_position_embeddings

        # Input embeddings
        self.token_embeddings = nn.Embedding(config.vocab_size, self.hidden_size)
        self.position_embeddings = nn.Embedding(config.max_position_embeddings, self.hidden_size)

        # Transformer blocks
        self.blocks = nn.ModuleList([
            LLMBlock(
                hidden_size=self.hidden_size,
                num_heads=self.num_heads,
                intermediate_size=config.intermediate_size,
                dropout=config.dropout_prob,
                use_flash_attention=config.use_flash_attention,
            )
            for _ in range(self.num_layers)
        ])

        # Output layers
        self.ln_f = nn.LayerNorm(self.hidden_size)
        self.lm_head = nn.Linear(self.hidden_size, config.vocab_size, bias=False)

        # Projection layer for integrating ThinkerModule outputs
        self.thinker_projection = self._create_projection_layer(config)

        # Integration layers
        self.integration_layers = nn.ModuleList([
            nn.Linear(self.hidden_size * 2, self.hidden_size)
            for _ in range(config.num_layers // 2)  # Apply integration at middle layers
        ])

        # Initialize weights
        self.apply(self._init_weights)

        # Tie embeddings if specified
        if config.tie_word_embeddings:
            self.lm_head.weight = self.token_embeddings.weight

    def _resize_embeddings(self, new_vocab_size: int):
        """Resize token embeddings and output layer."""
        old_vocab_size = self.vocab_size

        if new_vocab_size == old_vocab_size:
            return

        # Resize token embeddings
        old_embeddings = self.token_embeddings
        new_embeddings = nn.Embedding(new_vocab_size, self.hidden_size)

        # Copy old weights
        with torch.no_grad():
            new_embeddings.weight[:old_vocab_size] = old_embeddings.weight
            # Initialize new tokens with small random values
            if new_vocab_size > old_vocab_size:
                new_embeddings.weight[old_vocab_size:].normal_(mean=0.0, std=0.02)

        self.token_embeddings = new_embeddings

        # Resize output layer
        old_lm_head = self.lm_head
        new_lm_head = nn.Linear(self.hidden_size, new_vocab_size, bias=False)

        # Copy old weights
        with torch.no_grad():
            new_lm_head.weight[:old_vocab_size] = old_lm_head.weight
            # Initialize new tokens with small random values
            if new_vocab_size > old_vocab_size:
                new_lm_head.weight[old_vocab_size:].normal_(mean=0.0, std=0.02)

        self.lm_head = new_lm_head

        # Update vocab size
        self.vocab_size = new_vocab_size

        # Re-tie embeddings if specified
        if self.config.tie_word_embeddings:
            self.lm_head.weight = self.token_embeddings.weight

    def _create_projection_layer(self, config: ModelConfig):
        """Create the appropriate projection layer based on configuration."""
        if config.projection_type == "linear":
            return ProjectionLayer(
                input_dim=config.thinker_hidden_size,
                output_dim=config.hidden_size,
                hidden_dim=config.projection_hidden_size,
                dropout=config.dropout_prob,
            )
        elif config.projection_type == "adaptive":
            return AdaptiveProjection(
                input_dim=config.thinker_hidden_size,
                output_dim=config.hidden_size,
                num_experts=4,
                hidden_dim=config.projection_hidden_size,
                dropout=config.dropout_prob,
            )
        elif config.projection_type == "cross_attention":
            return CrossAttentionProjection(
                llm_dim=config.hidden_size,
                thinker_dim=config.thinker_hidden_size,
                num_heads=config.num_attention_heads,
                hidden_dim=config.projection_hidden_size,
                dropout=config.dropout_prob,
            )
        else:
            raise ValueError(f"Unknown projection type: {config.projection_type}")

    def _init_weights(self, module):
        """Initialize weights using standard transformer initialization."""
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)

    def forward(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        reasoning_states: Optional[torch.Tensor] = None,
        reasoning_mask: Optional[torch.Tensor] = None,
        labels: Optional[torch.Tensor] = None,
        return_dict: bool = True,
    ) -> Dict[str, torch.Tensor]:
        """
        Forward pass through MainLLM.

        Args:
            input_ids: Input token IDs (batch_size, seq_len)
            attention_mask: Optional attention mask (batch_size, seq_len)
            reasoning_states: Optional reasoning states from ThinkerModule
            reasoning_mask: Optional mask for reasoning states
            labels: Optional labels for computing loss
            return_dict: Whether to return a dictionary

        Returns:
            Dictionary containing:
                - logits: Language modeling logits
                - hidden_states: Final hidden states
                - loss: Language modeling loss (if labels provided)
                - attention_weights: Attention weights from all layers
        """
        batch_size, seq_len = input_ids.shape
        device = input_ids.device

        # Clamp input_ids to valid range to prevent out-of-bounds access
        # Use self.vocab_size which gets updated during resize
        input_ids = torch.clamp(input_ids, 0, self.vocab_size - 1)

        # Create position IDs - clamp to max_position_embeddings to prevent out-of-bounds access
        position_ids = torch.arange(seq_len, device=device).unsqueeze(0).expand(batch_size, -1)
        position_ids = torch.clamp(position_ids, 0, self.max_position_embeddings - 1)

        # Embeddings
        token_embeds = self.token_embeddings(input_ids)
        position_embeds = self.position_embeddings(position_ids)
        hidden_states = token_embeds + position_embeds

        # Apply dropout
        hidden_states = F.dropout(hidden_states, p=self.config.dropout_prob, training=self.training)

        # Project reasoning states if provided
        projected_reasoning = None
        if reasoning_states is not None:
            if self.config.projection_type == "cross_attention":
                # Cross-attention projection will be applied during forward pass
                projected_reasoning = reasoning_states
            else:
                # Direct projection
                if isinstance(self.thinker_projection, AdaptiveProjection):
                    projected_reasoning, _ = self.thinker_projection(reasoning_states)
                else:
                    projected_reasoning = self.thinker_projection(reasoning_states)

        # Pass through transformer blocks
        all_attention_weights = []
        integration_layer_idx = 0

        for layer_idx, block in enumerate(self.blocks):
            # Apply transformer block
            hidden_states, attn_weights = block(
                hidden_states=hidden_states,
                attention_mask=attention_mask,
                position_embeddings=None,  # Already added
            )
            all_attention_weights.append(attn_weights)

            # Integrate reasoning states at middle layers
            if (projected_reasoning is not None and
                layer_idx >= len(self.blocks) // 4 and
                layer_idx < 3 * len(self.blocks) // 4 and
                integration_layer_idx < len(self.integration_layers)):

                if self.config.projection_type == "cross_attention":
                    # Use cross-attention for integration
                    enhanced_states, _ = self.thinker_projection(
                        llm_states=hidden_states,
                        thinker_states=projected_reasoning,
                        thinker_mask=reasoning_mask,
                    )
                    hidden_states = enhanced_states
                else:
                    # Concatenate and project
                    if projected_reasoning.size(1) != hidden_states.size(1):
                        # Handle sequence length mismatch
                        if projected_reasoning.size(1) < hidden_states.size(1):
                            # Pad reasoning states
                            pad_length = hidden_states.size(1) - projected_reasoning.size(1)
                            padding = torch.zeros(
                                batch_size, pad_length, projected_reasoning.size(-1),
                                device=device, dtype=projected_reasoning.dtype
                            )
                            projected_reasoning = torch.cat([projected_reasoning, padding], dim=1)
                        else:
                            # Truncate reasoning states
                            projected_reasoning = projected_reasoning[:, :hidden_states.size(1), :]

                    # Concatenate and integrate
                    combined_states = torch.cat([hidden_states, projected_reasoning], dim=-1)
                    hidden_states = self.integration_layers[integration_layer_idx](combined_states)
                    integration_layer_idx += 1

        # Final layer norm
        hidden_states = self.ln_f(hidden_states)

        # Language modeling head
        logits = self.lm_head(hidden_states)

        # Compute loss if labels are provided
        loss = None
        if labels is not None:
            # Shift so that tokens < n predict n
            shift_logits = logits[..., :-1, :].contiguous()
            shift_labels = labels[..., 1:].contiguous()

            # Flatten the tokens
            loss_fct = nn.CrossEntropyLoss()
            loss = loss_fct(shift_logits.view(-1, shift_logits.size(-1)), shift_labels.view(-1))

        if return_dict:
            return {
                "logits": logits,
                "hidden_states": hidden_states,
                "loss": loss,
                "attention_weights": all_attention_weights,
            }
        else:
            return logits, hidden_states, loss, all_attention_weights

    def generate(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        reasoning_states: Optional[torch.Tensor] = None,
        reasoning_mask: Optional[torch.Tensor] = None,
        max_new_tokens: int = 512,
        temperature: float = 0.7,
        top_p: float = 0.9,
        top_k: int = 50,
        do_sample: bool = True,
        num_beams: int = 1,
        pad_token_id: Optional[int] = None,
        eos_token_id: Optional[int] = None,
    ) -> torch.Tensor:
        """
        Generate text using the MainLLM.

        Args:
            input_ids: Input token IDs
            attention_mask: Optional attention mask
            reasoning_states: Optional reasoning states from ThinkerModule
            reasoning_mask: Optional mask for reasoning states
            max_new_tokens: Maximum number of new tokens to generate
            temperature: Sampling temperature
            top_p: Top-p sampling parameter
            top_k: Top-k sampling parameter
            do_sample: Whether to use sampling
            num_beams: Number of beams for beam search
            pad_token_id: Padding token ID
            eos_token_id: End-of-sequence token ID

        Returns:
            Generated token IDs
        """
        self.eval()

        batch_size = input_ids.size(0)
        device = input_ids.device

        # Initialize generation
        generated_ids = input_ids.clone()

        if attention_mask is not None:
            current_mask = attention_mask.clone()
        else:
            current_mask = torch.ones_like(input_ids)

        with torch.no_grad():
            for _ in range(max_new_tokens):
                # Forward pass
                outputs = self.forward(
                    input_ids=generated_ids,
                    attention_mask=current_mask,
                    reasoning_states=reasoning_states,
                    reasoning_mask=reasoning_mask,
                    return_dict=True,
                )

                # Get next token logits
                next_token_logits = outputs["logits"][:, -1, :]

                # Apply temperature
                if temperature != 1.0:
                    next_token_logits = next_token_logits / temperature

                # Apply top-k filtering
                if top_k > 0:
                    top_k_logits, top_k_indices = torch.topk(next_token_logits, top_k, dim=-1)
                    next_token_logits = torch.full_like(next_token_logits, float('-inf'))
                    next_token_logits.scatter_(dim=-1, index=top_k_indices, src=top_k_logits)

                # Apply top-p filtering
                if top_p < 1.0:
                    sorted_logits, sorted_indices = torch.sort(next_token_logits, descending=True, dim=-1)
                    cumulative_probs = torch.cumsum(F.softmax(sorted_logits, dim=-1), dim=-1)

                    # Remove tokens with cumulative probability above the threshold
                    sorted_indices_to_remove = cumulative_probs > top_p
                    sorted_indices_to_remove[:, 1:] = sorted_indices_to_remove[:, :-1].clone()
                    sorted_indices_to_remove[:, 0] = 0

                    # Scatter sorted tensors to original indexing
                    indices_to_remove = sorted_indices_to_remove.scatter(
                        dim=-1, index=sorted_indices, src=sorted_indices_to_remove
                    )
                    next_token_logits[indices_to_remove] = float('-inf')

                # Sample next token
                if do_sample:
                    probs = F.softmax(next_token_logits, dim=-1)
                    next_tokens = torch.multinomial(probs, num_samples=1)
                else:
                    next_tokens = torch.argmax(next_token_logits, dim=-1, keepdim=True)

                # Append to generated sequence
                generated_ids = torch.cat([generated_ids, next_tokens], dim=-1)

                # Update attention mask
                current_mask = torch.cat([
                    current_mask,
                    torch.ones(batch_size, 1, device=device, dtype=current_mask.dtype)
                ], dim=-1)

                # Check for EOS token
                if eos_token_id is not None and (next_tokens == eos_token_id).all():
                    break

        return generated_ids
