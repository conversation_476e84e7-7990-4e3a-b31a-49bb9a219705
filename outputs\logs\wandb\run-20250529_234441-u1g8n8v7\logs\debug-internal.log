{"time":"2025-05-29T23:44:41.9078386+07:00","level":"INFO","msg":"stream: starting","core version":"0.19.8","symlink path":"outputs\\logs\\wandb\\run-20250529_234441-u1g8n8v7\\logs\\debug-core.log"}
{"time":"2025-05-29T23:44:42.019031+07:00","level":"INFO","msg":"created new stream","id":"u1g8n8v7"}
{"time":"2025-05-29T23:44:42.019031+07:00","level":"INFO","msg":"stream: started","id":"u1g8n8v7"}
{"time":"2025-05-29T23:44:42.019031+07:00","level":"INFO","msg":"writer: Do: started","stream_id":"u1g8n8v7"}
{"time":"2025-05-29T23:44:42.019031+07:00","level":"INFO","msg":"handler: started","stream_id":"u1g8n8v7"}
{"time":"2025-05-29T23:44:42.019031+07:00","level":"INFO","msg":"sender: started","stream_id":"u1g8n8v7"}
{"time":"2025-05-29T23:44:42.4293422+07:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-05-29T23:44:57.4422793+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:45:12.4409001+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:45:27.4432484+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:45:42.4409002+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:45:57.452065+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:46:12.4404263+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:46:27.4442037+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:46:42.4417344+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:46:57.4400802+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:47:12.4398883+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:47:27.4400442+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:47:42.4402144+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:47:57.4403086+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:48:12.4420518+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:48:27.4401814+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:48:42.4409+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:48:57.4405998+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:49:12.4404215+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:49:27.4397773+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:49:42.4409014+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:49:57.4401239+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:50:12.4403792+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:50:27.4420432+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:50:42.4392241+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:50:57.4399433+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:51:12.4401042+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:51:27.4393405+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:51:42.4442061+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:51:57.4454362+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:52:12.4400531+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:52:27.4402775+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:52:42.4424695+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:52:57.4391214+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:53:12.4421233+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:53:27.4409947+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:53:42.4420645+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:53:57.4404418+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:54:12.4403407+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:54:27.4397031+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:54:42.4409344+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:54:57.4403568+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:55:12.4403303+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:55:27.4397278+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:55:42.4407675+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:55:57.4414141+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:56:12.4424031+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:56:27.443301+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:56:42.4400786+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:56:57.4410493+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:57:12.441929+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:57:27.4410578+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:57:42.4431182+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:57:57.4449383+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:58:12.4513309+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:58:27.4424656+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:58:42.4411816+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:58:57.4396127+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:59:12.4401979+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:59:27.4395611+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:59:42.4420179+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-29T23:59:57.4466716+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:00:12.4489495+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:00:27.4597183+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:00:42.4487514+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:00:57.4484462+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:01:12.4478062+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:01:27.449894+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:01:42.4485472+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:01:57.4442036+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:02:12.4392687+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:02:27.4411006+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:02:42.4399268+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:02:57.4521041+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:03:12.4431863+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:03:27.4462383+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:03:42.4414431+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:03:57.4420265+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:04:12.4403804+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:04:27.4404609+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:04:42.4405212+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:04:57.4522756+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:05:12.4405473+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:05:27.440003+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:05:42.4403173+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:05:57.43956+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:06:12.4445081+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:06:27.4397118+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:06:42.4405073+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:06:57.4413534+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:07:12.4426361+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:07:27.4395723+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:07:42.4414261+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:07:57.4406118+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:08:12.4442888+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:08:27.4461873+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:08:42.4404729+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:08:57.4496373+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:09:12.4456612+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:09:27.4403538+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:09:42.4593473+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:09:57.4398588+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:10:12.4396696+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:10:27.4399216+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:10:42.4406151+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:10:57.4421748+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:11:12.4399936+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:11:27.440274+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:11:42.4396841+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:11:57.4407108+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:12:12.4539985+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:12:27.4405034+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:12:42.4396661+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:12:57.4406122+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:13:12.4392763+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:13:27.4405786+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:13:42.4412378+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:13:57.4410523+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:14:12.4405712+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:14:27.4431519+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:14:42.4408968+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:14:57.4396589+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:15:12.4404821+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:15:27.4403766+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:15:42.4408528+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:15:57.4400843+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:16:12.4404253+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:16:27.445312+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:16:42.4406142+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:16:57.4400423+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:17:12.439134+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:17:27.4413495+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:17:42.4406583+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:17:57.4406236+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:18:12.441247+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:18:27.455792+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:18:42.4406161+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:18:57.4408097+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:19:12.4415233+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:19:27.4425881+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:19:42.449028+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:19:57.448077+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:20:12.4468385+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:20:27.4438937+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:20:42.4421818+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:20:57.4455536+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:21:12.4464402+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:21:27.4484644+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:21:42.4400323+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:21:57.4422592+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:22:12.4409964+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:22:27.4395452+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:22:42.4409605+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:22:57.4406351+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:23:12.4407666+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:23:27.4392742+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:23:42.4395522+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:23:57.4401563+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:24:12.4397142+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:24:27.4407782+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:24:42.4396539+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:24:57.4404054+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:25:12.4417488+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:25:27.4418324+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:25:42.4393665+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:25:57.4416045+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:26:12.4423104+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:26:27.4411992+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:26:42.4401781+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:26:57.4413306+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:27:12.4397975+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:27:27.4420224+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:27:42.4419159+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:27:57.4399326+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:28:12.4419784+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:28:27.4402227+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:28:42.4416709+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:28:57.4434146+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:29:12.4405607+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:29:27.4416076+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:29:42.442625+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:29:57.4505134+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:30:12.4422102+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:30:27.4404049+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:30:42.4417193+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:30:57.450998+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:31:12.4522093+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:31:27.4424318+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:31:42.4405312+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:31:57.4402065+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:32:12.440865+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:32:27.4402008+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:32:42.4400875+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:32:57.4399864+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:33:12.4397705+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:33:27.4434715+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:33:42.4403051+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:33:57.4399291+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:34:12.444609+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:34:27.4395469+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:34:42.4410248+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:34:57.4398892+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:35:12.4400651+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:35:27.4417586+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:35:42.4400197+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:35:57.4398739+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:36:12.4398674+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:36:27.440844+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:36:42.4428591+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:36:57.4397+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:37:12.4405076+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:37:27.4395386+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:37:42.4403147+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:37:57.4410234+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:38:12.4395449+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:38:27.4399044+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:38:42.4435179+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:38:57.441942+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:39:12.4396599+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:39:27.4394853+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:39:42.4395794+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:39:57.4395171+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:40:12.440743+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:40:27.4399544+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:40:42.4391975+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:40:57.4405321+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:41:12.4402041+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:41:27.4458118+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:41:42.4549267+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:41:57.4417201+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:42:12.4399191+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:42:27.4467519+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:42:42.4412739+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:42:57.4409661+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:43:12.4398388+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:43:27.4460721+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:43:42.4550525+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:43:57.4442165+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:44:12.4414171+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:44:27.440126+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:44:42.4399045+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:44:57.4408298+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:45:12.4433024+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:45:27.4410118+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:45:42.4396842+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:45:57.440173+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:46:12.4409103+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:46:27.4398941+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:46:42.4393366+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:46:57.445021+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:47:12.4392525+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:47:27.4398504+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:47:42.4400672+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:47:57.4417101+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:48:12.4404056+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:48:27.4403631+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:48:42.4403648+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:48:57.4393901+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:49:12.4435998+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:49:27.4438095+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:49:42.448046+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:49:57.4403974+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:50:12.4418407+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:50:27.4401509+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:50:42.4437039+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:50:57.4391968+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:51:12.4400584+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:51:27.440481+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:51:42.4395199+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:51:57.4392948+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:52:12.4406587+07:00","level":"ERROR","msg":"monitor: error sampling metrics: Incorrect function."}
{"time":"2025-05-30T00:52:17.3999018+07:00","level":"INFO","msg":"stream: closing","id":"u1g8n8v7"}
{"time":"2025-05-30T00:52:17.3999018+07:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-05-30T00:52:17.4106811+07:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-05-30T00:52:19.0438925+07:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-05-30T00:52:19.3749945+07:00","level":"INFO","msg":"handler: closed","stream_id":"u1g8n8v7"}
{"time":"2025-05-30T00:52:19.3749945+07:00","level":"INFO","msg":"writer: Close: closed","stream_id":"u1g8n8v7"}
{"time":"2025-05-30T00:52:19.3765168+07:00","level":"INFO","msg":"sender: closed","stream_id":"u1g8n8v7"}
{"time":"2025-05-30T00:52:19.3765168+07:00","level":"INFO","msg":"stream: closed","id":"u1g8n8v7"}
