#!/usr/bin/env python3
"""
Simple test script for web UI components
"""

import sys
import os
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent))

def test_imports():
    """Test all required imports."""
    print("Testing imports...")
    
    try:
        import torch
        print("✅ PyTorch imported")
        
        import gradio as gr
        print("✅ Gradio imported")
        
        from transformers import AutoTokenizer
        print("✅ Transformers imported")
        
        from thinker_llm.models import IntegratedThinkerLLM
        print("✅ ThinkerLLM model imported")
        
        from thinker_llm.utils.config import ModelConfig
        print("✅ ModelConfig imported")
        
        from thinker_llm.inference.generator import ThinkerGenerator
        print("✅ ThinkerGenerator imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_model_creation():
    """Test model creation."""
    print("\nTesting model creation...")
    
    try:
        import torch
        from transformers import AutoTokenizer
        from thinker_llm.models import IntegratedThinkerLL<PERSON>
        from thinker_llm.utils.config import ModelConfig
        
        # Setup tokenizer
        tokenizer = AutoTokenizer.from_pretrained("gpt2")
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        # Add special tokens
        special_tokens = ["<|thinking|>", "<|/thinking|>", "<|response|>", "<|/response|>"]
        new_tokens = [token for token in special_tokens if token not in tokenizer.get_vocab()]
        if new_tokens:
            tokenizer.add_tokens(new_tokens)
        
        print(f"✅ Tokenizer created with vocab size: {len(tokenizer)}")
        
        # Create model
        model_config = ModelConfig()
        model_config.vocab_size = len(tokenizer)
        model_config.use_flash_attention = False
        
        model = IntegratedThinkerLLM(model_config)
        model.resize_token_embeddings(len(tokenizer))
        
        print(f"✅ Model created with {sum(p.numel() for p in model.parameters()):,} parameters")
        
        return True
        
    except Exception as e:
        print(f"❌ Model creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_generator():
    """Test generator creation."""
    print("\nTesting generator...")
    
    try:
        import torch
        from transformers import AutoTokenizer
        from thinker_llm.models import IntegratedThinkerLLM
        from thinker_llm.utils.config import ModelConfig
        from thinker_llm.inference.generator import ThinkerGenerator
        
        # Setup tokenizer and model (simplified)
        tokenizer = AutoTokenizer.from_pretrained("gpt2")
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        special_tokens = ["<|thinking|>", "<|/thinking|>", "<|response|>", "<|/response|>"]
        new_tokens = [token for token in special_tokens if token not in tokenizer.get_vocab()]
        if new_tokens:
            tokenizer.add_tokens(new_tokens)
        
        model_config = ModelConfig()
        model_config.vocab_size = len(tokenizer)
        model_config.use_flash_attention = False
        
        model = IntegratedThinkerLLM(model_config)
        model.resize_token_embeddings(len(tokenizer))
        model.eval()
        
        # Create generator
        generator = ThinkerGenerator(model, tokenizer)
        print("✅ Generator created successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Generator creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gradio_interface():
    """Test Gradio interface creation."""
    print("\nTesting Gradio interface...")
    
    try:
        import gradio as gr
        
        # Create a simple interface
        def dummy_function(text):
            return f"Echo: {text}"
        
        interface = gr.Interface(
            fn=dummy_function,
            inputs=gr.Textbox(label="Input"),
            outputs=gr.Textbox(label="Output"),
            title="Test Interface"
        )
        
        print("✅ Gradio interface created successfully")
        return True
        
    except Exception as e:
        print(f"❌ Gradio interface creation failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing ThinkerLLM Web UI Components")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_model_creation,
        test_generator,
        test_gradio_interface
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"✅ Passed: {sum(results)}/{len(results)}")
    print(f"❌ Failed: {len(results) - sum(results)}/{len(results)}")
    
    if all(results):
        print("\n🎉 All tests passed! Web UI should work correctly.")
    else:
        print("\n⚠️  Some tests failed. Please check the errors above.")

if __name__ == "__main__":
    main()
