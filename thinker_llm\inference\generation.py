"""
Generation utilities for ThinkerLLM inference.
"""

import torch
from typing import Dict, Any, Optional, <PERSON><PERSON>
from transformers import AutoTokenizer

from ..models.integrated_model import IntegratedThinkerLLM
from .config import InferenceConfig


class ThinkerGenerator:
    """
    Generator for thinking processes using ThinkerModule.
    """
    
    def __init__(
        self,
        model: IntegratedThinkerLLM,
        tokenizer: AutoTokenizer,
        config: InferenceConfig,
    ):
        self.model = model
        self.tokenizer = tokenizer
        self.config = config
    
    def generate_thoughts(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        **kwargs
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Generate thoughts using ThinkerModule.
        
        Args:
            input_ids: Input token IDs
            attention_mask: Optional attention mask
            **kwargs: Additional generation parameters
            
        Returns:
            Tuple of (thought_ids, reasoning_states)
        """
        # Extract thinking-specific parameters
        thinking_params = {
            "max_new_tokens": kwargs.get("max_thinking_tokens", self.config.max_thinking_tokens),
            "temperature": kwargs.get("thinking_temperature", self.config.thinking_temperature),
            "top_p": kwargs.get("top_p", self.config.top_p),
            "do_sample": kwargs.get("do_sample", self.config.do_sample),
        }
        
        # Generate thoughts
        thought_ids, reasoning_states = self.model.thinker_module.generate_thoughts(
            input_ids=input_ids,
            attention_mask=attention_mask,
            **thinking_params
        )
        
        return thought_ids, reasoning_states
    
    def analyze_thinking_quality(
        self,
        thought_ids: torch.Tensor,
        reasoning_states: torch.Tensor,
    ) -> Dict[str, float]:
        """
        Analyze the quality of generated thoughts.
        
        Args:
            thought_ids: Generated thought token IDs
            reasoning_states: Generated reasoning states
            
        Returns:
            Dictionary with quality metrics
        """
        metrics = {}
        
        # Diversity metric
        unique_tokens = len(torch.unique(thought_ids))
        total_tokens = thought_ids.numel()
        metrics["diversity"] = unique_tokens / max(total_tokens, 1)
        
        # Coherence metric (simplified)
        # Based on reasoning state consistency
        if reasoning_states.size(0) > 1:
            state_similarities = torch.cosine_similarity(
                reasoning_states[:-1], 
                reasoning_states[1:], 
                dim=-1
            )
            metrics["coherence"] = state_similarities.mean().item()
        else:
            metrics["coherence"] = 1.0
        
        # Length metric
        metrics["length"] = total_tokens
        
        # Complexity metric (based on reasoning state variance)
        metrics["complexity"] = reasoning_states.var().item()
        
        return metrics


class StandardGenerator:
    """
    Generator for standard (non-thinking) responses using MainLLM.
    """
    
    def __init__(
        self,
        model: IntegratedThinkerLLM,
        tokenizer: AutoTokenizer,
        config: InferenceConfig,
    ):
        self.model = model
        self.tokenizer = tokenizer
        self.config = config
    
    def generate_response(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        reasoning_states: Optional[torch.Tensor] = None,
        reasoning_mask: Optional[torch.Tensor] = None,
        **kwargs
    ) -> torch.Tensor:
        """
        Generate response using MainLLM.
        
        Args:
            input_ids: Input token IDs
            attention_mask: Optional attention mask
            reasoning_states: Optional reasoning states from ThinkerModule
            reasoning_mask: Optional mask for reasoning states
            **kwargs: Additional generation parameters
            
        Returns:
            Generated token IDs
        """
        # Extract generation parameters
        generation_params = {
            "max_new_tokens": kwargs.get("max_new_tokens", self.config.max_new_tokens),
            "temperature": kwargs.get("temperature", self.config.temperature),
            "top_p": kwargs.get("top_p", self.config.top_p),
            "top_k": kwargs.get("top_k", self.config.top_k),
            "do_sample": kwargs.get("do_sample", self.config.do_sample),
            "num_beams": kwargs.get("num_beams", self.config.num_beams),
        }
        
        # Generate response
        generated_ids = self.model.main_llm.generate(
            input_ids=input_ids,
            attention_mask=attention_mask,
            reasoning_states=reasoning_states,
            reasoning_mask=reasoning_mask,
            **generation_params
        )
        
        return generated_ids
    
    def analyze_response_quality(
        self,
        input_ids: torch.Tensor,
        generated_ids: torch.Tensor,
        reasoning_states: Optional[torch.Tensor] = None,
    ) -> Dict[str, float]:
        """
        Analyze the quality of generated responses.
        
        Args:
            input_ids: Original input token IDs
            generated_ids: Generated response token IDs
            reasoning_states: Optional reasoning states used
            
        Returns:
            Dictionary with quality metrics
        """
        metrics = {}
        
        # Extract only new tokens
        input_length = input_ids.size(1)
        new_tokens = generated_ids[:, input_length:]
        
        # Length metric
        metrics["response_length"] = new_tokens.size(1)
        
        # Diversity metric
        unique_tokens = len(torch.unique(new_tokens))
        total_tokens = new_tokens.numel()
        metrics["diversity"] = unique_tokens / max(total_tokens, 1)
        
        # Repetition metric
        if total_tokens > 1:
            # Count repeated bigrams
            bigrams = []
            for i in range(new_tokens.size(1) - 1):
                bigram = (new_tokens[0, i].item(), new_tokens[0, i + 1].item())
                bigrams.append(bigram)
            
            unique_bigrams = len(set(bigrams))
            metrics["repetition"] = 1.0 - (unique_bigrams / max(len(bigrams), 1))
        else:
            metrics["repetition"] = 0.0
        
        # Reasoning integration metric (if reasoning states were used)
        if reasoning_states is not None:
            # This is a simplified metric - in practice, you might want
            # to measure how well the response incorporates the reasoning
            metrics["reasoning_integration"] = 1.0
        else:
            metrics["reasoning_integration"] = 0.0
        
        return metrics


class AdaptiveGenerator:
    """
    Adaptive generator that adjusts parameters based on input characteristics.
    """
    
    def __init__(
        self,
        model: IntegratedThinkerLLM,
        tokenizer: AutoTokenizer,
        config: InferenceConfig,
    ):
        self.model = model
        self.tokenizer = tokenizer
        self.config = config
        self.thinker_generator = ThinkerGenerator(model, tokenizer, config)
        self.standard_generator = StandardGenerator(model, tokenizer, config)
    
    def adapt_parameters(
        self,
        input_ids: torch.Tensor,
        decision_info: Dict[str, torch.Tensor],
    ) -> Dict[str, Any]:
        """
        Adapt generation parameters based on input and decision info.
        
        Args:
            input_ids: Input token IDs
            decision_info: Decision mechanism outputs
            
        Returns:
            Adapted generation parameters
        """
        params = {}
        
        # Get input characteristics
        input_length = input_ids.size(1)
        thinking_prob = decision_info["thinking_probability"].item()
        
        # Adapt temperature based on thinking probability
        if thinking_prob > 0.7:
            # High thinking probability - use lower temperature for more focused generation
            params["temperature"] = max(self.config.temperature * 0.8, 0.1)
        elif thinking_prob < 0.3:
            # Low thinking probability - use higher temperature for more creative generation
            params["temperature"] = min(self.config.temperature * 1.2, 1.0)
        else:
            params["temperature"] = self.config.temperature
        
        # Adapt max_new_tokens based on input length
        if input_length > 1000:
            # Long input - generate shorter response
            params["max_new_tokens"] = max(self.config.max_new_tokens // 2, 50)
        elif input_length < 100:
            # Short input - can generate longer response
            params["max_new_tokens"] = min(self.config.max_new_tokens * 1.5, 1024)
        else:
            params["max_new_tokens"] = self.config.max_new_tokens
        
        # Adapt thinking parameters
        if thinking_prob > 0.5:
            params["max_thinking_tokens"] = min(self.config.max_thinking_tokens * 1.5, 512)
            params["thinking_temperature"] = self.config.thinking_temperature * 0.9
        else:
            params["max_thinking_tokens"] = self.config.max_thinking_tokens
            params["thinking_temperature"] = self.config.thinking_temperature
        
        return params
    
    def generate(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        **kwargs
    ) -> Dict[str, torch.Tensor]:
        """
        Generate response with adaptive parameters.
        
        Args:
            input_ids: Input token IDs
            attention_mask: Optional attention mask
            **kwargs: Additional generation parameters
            
        Returns:
            Dictionary with generated outputs
        """
        # Get decision info
        should_think_mask, decision_info = self.model.decision_mechanism.should_think(
            input_ids=input_ids,
            attention_mask=attention_mask,
        )
        
        # Adapt parameters
        adapted_params = self.adapt_parameters(input_ids, decision_info)
        adapted_params.update(kwargs)  # Override with explicit kwargs
        
        # Generate thoughts if needed
        reasoning_states = None
        thought_ids = None
        
        if should_think_mask.any():
            thought_ids, reasoning_states = self.thinker_generator.generate_thoughts(
                input_ids=input_ids,
                attention_mask=attention_mask,
                **adapted_params
            )
        
        # Generate response
        generated_ids = self.standard_generator.generate_response(
            input_ids=input_ids,
            attention_mask=attention_mask,
            reasoning_states=reasoning_states,
            **adapted_params
        )
        
        return {
            "generated_ids": generated_ids,
            "thought_ids": thought_ids,
            "reasoning_states": reasoning_states,
            "decision_info": decision_info,
            "should_think_mask": should_think_mask,
            "adapted_params": adapted_params,
        }
