INFO:__main__:Starting training...
INFO:thinker_llm.training.trainer:Starting training...
INFO:thinker_llm.training.trainer:Total epochs: 1
INFO:thinker_llm.training.trainer:Total steps: 9972
Epoch 1/1:   0%|                                                                                         | 0/9972 [00:00<?, ?it/s, loss=5.5331, lr=0.00e+00]INFO:thinker_llm.training.trainer:train/loss: 5.5331
INFO:thinker_llm.training.trainer:train/learning_rate: 0.0000
INFO:thinker_llm.training.trainer:train/global_step: 0.0000
INFO:thinker_llm.training.trainer:Checkpoint saved: ./outputs\checkpoint_step_0.pt
ERROR:__main__:Training failed with error: CUDA error: device-side assert triggered                                                                         
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

Traceback (most recent call last):
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\scripts\train_thinker_llm.py", line 233, in <module>
    main()
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v4\scripts\train_thinker_llm.py", line 197, in main
    trainer.train()
  File "d:\vlinhd11\projects\lcms_llm\moe-llm\geneticai\v4\thinker_llm\training\trainer.py", line 293, in train
    epoch_metrics = self.train_epoch()
                    ^^^^^^^^^^^^^^^^^^
  File "d:\vlinhd11\projects\lcms_llm\moe-llm\geneticai\v4\thinker_llm\training\trainer.py", line 146, in train_epoch
    loss_dict = self.loss_fn(
                ^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\vlinhd11\projects\lcms_llm\moe-llm\geneticai\v4\thinker_llm\training\losses.py", line 375, in forward
    llm_losses = self.llm_loss(
                 ^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\vlinhd11\projects\lcms_llm\moe-llm\geneticai\v4\thinker_llm\training\losses.py", line 212, in forward
    integration_loss = self.mse_loss(integration_score, target_integration)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\loss.py", line 610, in forward
    return F.mse_loss(input, target, reduction=self.reduction)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\functional.py", line 3905, in mse_loss
    return torch._C._nn.mse_loss(
           ^^^^^^^^^^^^^^^^^^^^^^
RuntimeError: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.
