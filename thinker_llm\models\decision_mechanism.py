"""
Decision Mechanism: Router that determines whether thinking is needed.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Dict, Any, List, Tuple
import re
import numpy as np

from ..utils.config import ModelConfig


class FeatureExtractor(nn.Module):
    """
    Extract features from input text to help decision making.
    """

    def __init__(self, vocab_size: int, hidden_size: int, max_length: int):
        super().__init__()
        self.vocab_size = vocab_size
        self.hidden_size = hidden_size
        self.max_length = max_length

        # Simple embedding for feature extraction
        self.embeddings = nn.Embedding(vocab_size, hidden_size)
        self.pooling = nn.AdaptiveAvgPool1d(1)

    def forward(self, input_ids: torch.Tensor, attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Extract features from input."""
        # Clamp input_ids to valid range to prevent out-of-bounds access
        input_ids = torch.clamp(input_ids, 0, self.vocab_size - 1)

        embeds = self.embeddings(input_ids)  # (batch_size, seq_len, hidden_size)

        if attention_mask is not None:
            # Ensure attention_mask is the right type and shape
            attention_mask = attention_mask.bool()
            if attention_mask.dim() == 2:
                # Mask out padding tokens
                mask = attention_mask.unsqueeze(-1).expand_as(embeds)
                embeds = embeds * mask.float()

                # Compute weighted average
                lengths = attention_mask.sum(dim=1, keepdim=True).float()
                lengths = torch.clamp(lengths, min=1.0)  # Avoid division by zero
                features = embeds.sum(dim=1) / lengths
            else:
                # Fallback to simple mean if attention_mask has wrong shape
                features = embeds.mean(dim=1)
        else:
            # Simple average pooling
            features = embeds.mean(dim=1)

        return features


class DecisionMechanism(nn.Module):
    """
    Decision mechanism that determines whether thinking is needed for a given input.
    Uses multiple features including input complexity, length, and content analysis.
    """

    def __init__(self, config: ModelConfig):
        super().__init__()
        self.config = config
        self.hidden_size = config.decision_hidden_size
        self.num_layers = config.decision_num_layers
        self.threshold = config.decision_threshold
        self.vocab_size = config.vocab_size
        self.max_length = config.max_position_embeddings

        # Feature extractors
        self.text_feature_extractor = FeatureExtractor(
            vocab_size=config.vocab_size,
            hidden_size=self.hidden_size,
            max_length=config.max_position_embeddings,
        )

        # Statistical feature dimensions
        self.stat_feature_dim = 10  # Length, complexity, etc.

        # Decision network
        total_feature_dim = self.hidden_size + self.stat_feature_dim

        layers = []
        current_dim = total_feature_dim

        for i in range(self.num_layers):
            next_dim = self.hidden_size // (2 ** i) if i < self.num_layers - 1 else 1
            layers.extend([
                nn.Linear(current_dim, next_dim),
                nn.GELU() if i < self.num_layers - 1 else nn.Sigmoid(),
                nn.Dropout(0.1) if i < self.num_layers - 1 else nn.Identity(),
            ])
            current_dim = next_dim

        self.decision_network = nn.Sequential(*layers)

        # Question type classifier (helps with decision making)
        self.question_classifier = nn.Sequential(
            nn.Linear(self.hidden_size, self.hidden_size // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(self.hidden_size // 2, 5),  # 5 question types
            nn.Softmax(dim=-1),
        )

        # Initialize weights
        self.apply(self._init_weights)

    def _resize_embeddings(self, new_vocab_size: int):
        """Resize embeddings in the feature extractor."""
        old_vocab_size = self.vocab_size

        if new_vocab_size == old_vocab_size:
            return

        # Update vocab size
        self.vocab_size = new_vocab_size

        # Resize feature extractor embeddings
        old_embeddings = self.text_feature_extractor.embeddings
        new_embeddings = nn.Embedding(new_vocab_size, old_embeddings.embedding_dim)

        # Copy old weights
        with torch.no_grad():
            new_embeddings.weight[:old_vocab_size] = old_embeddings.weight
            # Initialize new tokens with small random values
            if new_vocab_size > old_vocab_size:
                new_embeddings.weight[old_vocab_size:].normal_(mean=0.0, std=0.02)

        self.text_feature_extractor.embeddings = new_embeddings
        self.text_feature_extractor.vocab_size = new_vocab_size

    def _init_weights(self, module):
        """Initialize weights."""
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)

    def extract_statistical_features(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
    ) -> torch.Tensor:
        """
        Extract statistical features from input text.

        Args:
            input_ids: Input token IDs (batch_size, seq_len)
            attention_mask: Optional attention mask

        Returns:
            Statistical features (batch_size, stat_feature_dim)
        """
        batch_size, seq_len = input_ids.shape
        device = input_ids.device

        features = []

        for i in range(batch_size):
            sample_ids = input_ids[i]
            if attention_mask is not None:
                mask = attention_mask[i]
                valid_length = int(mask.sum().item())
                #print(f"Valid length: {valid_length}")
                sample_ids = sample_ids[:valid_length]
            else:
                valid_length = seq_len
                #print(f"Valid length: {valid_length}")

            # Feature 1: Input length (normalized)
            length_feature = min(valid_length / self.max_length, 1.0)

            # Feature 2: Vocabulary diversity (unique tokens / total tokens)
            unique_tokens = len(torch.unique(sample_ids))
            diversity_feature = unique_tokens / max(valid_length, 1)

            # Feature 3: Repetition score (1 - unique/total)
            repetition_feature = 1.0 - diversity_feature

            # Feature 4: Average token ID (complexity proxy)
            avg_token_id = sample_ids.float().mean().item() / self.vocab_size

            # Feature 5: Token ID variance (complexity proxy)
            token_variance = sample_ids.float().var().item() / (self.vocab_size ** 2)

            # Feature 6-10: N-gram complexity features (simplified)
            # Count of rare tokens (high IDs)
            rare_token_ratio = (sample_ids > self.vocab_size * 0.8).float().mean().item()

            # Count of common tokens (low IDs)
            common_token_ratio = (sample_ids < self.vocab_size * 0.1).float().mean().item()

            # Token transition complexity (simplified)
            if len(sample_ids) > 1:
                transitions = torch.abs(sample_ids[1:] - sample_ids[:-1]).float()
                transition_complexity = transitions.mean().item() / self.vocab_size
            else:
                transition_complexity = 0.0

            # Sequence entropy (simplified)
            token_counts = torch.bincount(sample_ids, minlength=min(self.vocab_size, 1000))
            token_probs = token_counts.float() / token_counts.sum()
            token_probs = token_probs[token_probs > 0]
            entropy = -(token_probs * torch.log(token_probs + 1e-8)).sum().item()
            entropy_normalized = min(entropy / 10.0, 1.0)

            # Pattern complexity (count of repeated subsequences)
            pattern_complexity = 0.5  # Simplified placeholder

            sample_features = torch.tensor([
                length_feature,
                diversity_feature,
                repetition_feature,
                avg_token_id,
                token_variance,
                rare_token_ratio,
                common_token_ratio,
                transition_complexity,
                entropy_normalized,
                pattern_complexity,
            ], device=device, dtype=torch.float32)

            features.append(sample_features)

        return torch.stack(features, dim=0)

    def classify_question_type(
        self,
        text_features: torch.Tensor,
    ) -> torch.Tensor:
        """
        Classify the type of question/input.

        Args:
            text_features: Text features from feature extractor

        Returns:
            Question type probabilities (batch_size, 5)
            Types: 0=factual, 1=reasoning, 2=creative, 3=analytical, 4=conversational
        """
        return self.question_classifier(text_features)

    def forward(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        return_features: bool = False,
    ) -> Dict[str, torch.Tensor]:
        """
        Forward pass through decision mechanism.

        Args:
            input_ids: Input token IDs (batch_size, seq_len)
            attention_mask: Optional attention mask
            return_features: Whether to return intermediate features

        Returns:
            Dictionary containing:
                - thinking_probability: Probability that thinking is needed
                - thinking_decision: Binary decision (0 or 1)
                - question_type: Question type classification
                - features: All extracted features (if return_features=True)
        """
        # Extract text features
        text_features = self.text_feature_extractor(input_ids, attention_mask)

        # Extract statistical features
        stat_features = self.extract_statistical_features(input_ids, attention_mask)

        # Combine features
        combined_features = torch.cat([text_features, stat_features], dim=-1)

        # Make decision
        thinking_probability = self.decision_network(combined_features).squeeze(-1)

        # Binary decision based on threshold
        thinking_decision = (thinking_probability > self.threshold).float()

        # Classify question type
        question_type = self.classify_question_type(text_features)

        result = {
            "thinking_probability": thinking_probability,
            "thinking_decision": thinking_decision,
            "question_type": question_type,
        }

        if return_features:
            result["features"] = {
                "text_features": text_features,
                "stat_features": stat_features,
                "combined_features": combined_features,
            }

        return result

    def should_think(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        force_thinking: bool = False,
        disable_thinking: bool = False,
    ) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        Determine whether thinking should be applied.

        Args:
            input_ids: Input token IDs
            attention_mask: Optional attention mask
            force_thinking: Force thinking regardless of decision
            disable_thinking: Disable thinking regardless of decision

        Returns:
            Tuple of (should_think_mask, decision_info)
        """
        if disable_thinking:
            batch_size = input_ids.size(0)
            should_think_mask = torch.zeros(batch_size, device=input_ids.device, dtype=torch.bool)
            decision_info = {
                "thinking_probability": torch.zeros(batch_size, device=input_ids.device),
                "thinking_decision": torch.zeros(batch_size, device=input_ids.device),
                "question_type": torch.zeros(batch_size, 5, device=input_ids.device),
            }
            return should_think_mask, decision_info

        if force_thinking:
            batch_size = input_ids.size(0)
            should_think_mask = torch.ones(batch_size, device=input_ids.device, dtype=torch.bool)
            decision_info = self.forward(input_ids, attention_mask)
            decision_info["thinking_decision"] = torch.ones(batch_size, device=input_ids.device)
            return should_think_mask, decision_info

        # Normal decision process
        decision_info = self.forward(input_ids, attention_mask)
        should_think_mask = decision_info["thinking_decision"].bool()

        return should_think_mask, decision_info

    def update_threshold(self, new_threshold: float):
        """Update the decision threshold."""
        self.threshold = new_threshold

    def get_decision_statistics(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
    ) -> Dict[str, float]:
        """
        Get detailed statistics about the decision process.

        Args:
            input_ids: Input token IDs
            attention_mask: Optional attention mask

        Returns:
            Dictionary with decision statistics
        """
        with torch.no_grad():
            outputs = self.forward(input_ids, attention_mask, return_features=True)

            stats = {
                "avg_thinking_probability": outputs["thinking_probability"].mean().item(),
                "thinking_ratio": outputs["thinking_decision"].mean().item(),
                "question_type_distribution": outputs["question_type"].mean(dim=0).tolist(),
                "feature_statistics": {
                    "text_feature_norm": outputs["features"]["text_features"].norm(dim=-1).mean().item(),
                    "stat_feature_mean": outputs["features"]["stat_features"].mean().item(),
                    "stat_feature_std": outputs["features"]["stat_features"].std().item(),
                }
            }

        return stats
